'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'

export default function Navbar() {
  const { user, profile, signOut, isAuthenticated } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-dark-900/95 backdrop-blur-luxury border-b border-dark-700/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-glow group-hover:shadow-glow-gold transition-all duration-300">
                <span className="text-white font-luxury font-bold text-xl">H</span>
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-luxury font-bold text-luxury">
                  Hotel Luxury
                </h1>
                <p className="text-xs text-dark-400 -mt-1">Management System</p>
              </div>
            </Link>
          </div>

          {/* Navigation Links - Desktop */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link
                href="/dashboard"
                className="text-dark-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-dark-800/50"
              >
                Dashboard
              </Link>
              <Link
                href="/dashboard"
                className="text-dark-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-dark-800/50"
              >
                Habitaciones
              </Link>
              <Link
                href="/reservas"
                className="text-dark-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-dark-800/50"
              >
                Reservas
              </Link>
              {profile?.role === 'admin' && (
                <Link
                  href="/dashboard/users"
                  className="text-dark-300 hover:text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-dark-800/50"
                >
                  Usuarios
                </Link>
              )}
            </div>
          </div>

          {/* User Menu */}
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6">
              {isAuthenticated ? (
                <div className="flex items-center space-x-4">
                  {/* User Info */}
                  <div className="flex items-center space-x-3">
                    {profile?.avatar_url ? (
                      <img
                        className="h-8 w-8 rounded-full ring-2 ring-primary-500/30"
                        src={profile.avatar_url}
                        alt={profile.first_name || 'User'}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-gradient-primary flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {profile?.first_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                        </span>
                      </div>
                    )}
                    <div className="hidden lg:block">
                      <div className="text-sm font-medium text-white">
                        {profile?.first_name} {profile?.last_name}
                      </div>
                      <div className="text-xs text-dark-400">
                        {profile?.role === 'admin' ? 'Administrador' : 
                         profile?.role === 'supervisor' ? 'Supervisor' : 'Trabajador'}
                      </div>
                    </div>
                  </div>

                  {/* Sign Out Button */}
                  <button
                    onClick={handleSignOut}
                    className="bg-dark-800 hover:bg-primary-600 text-dark-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 border border-dark-700 hover:border-primary-500"
                  >
                    Cerrar Sesión
                  </button>
                </div>
              ) : (
                <div className="flex items-center space-x-4">
                  <Link
                    href="/login"
                    className="text-dark-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  >
                    Iniciar Sesión
                  </Link>
                  <Link
                    href="/login"
                    className="btn-luxury text-sm"
                  >
                    Acceder
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="bg-dark-800 inline-flex items-center justify-center p-2 rounded-lg text-dark-400 hover:text-white hover:bg-dark-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-all duration-300"
            >
              <span className="sr-only">Open main menu</span>
              {!isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-dark-900/98 backdrop-blur-luxury border-t border-dark-700/50">
            <Link
              href="/dashboard"
              className="text-dark-300 hover:text-white block px-3 py-2 rounded-lg text-base font-medium transition-all duration-300 hover:bg-dark-800/50"
              onClick={() => setIsMenuOpen(false)}
            >
              Dashboard
            </Link>
            <Link
              href="/dashboard"
              className="text-dark-300 hover:text-white block px-3 py-2 rounded-lg text-base font-medium transition-all duration-300 hover:bg-dark-800/50"
              onClick={() => setIsMenuOpen(false)}
            >
              Habitaciones
            </Link>
            <Link
              href="/reservas"
              className="text-dark-300 hover:text-white block px-3 py-2 rounded-lg text-base font-medium transition-all duration-300 hover:bg-dark-800/50"
              onClick={() => setIsMenuOpen(false)}
            >
              Reservas
            </Link>
            {profile?.role === 'admin' && (
              <Link
                href="/dashboard/users"
                className="text-dark-300 hover:text-white block px-3 py-2 rounded-lg text-base font-medium transition-all duration-300 hover:bg-dark-800/50"
                onClick={() => setIsMenuOpen(false)}
              >
                Usuarios
              </Link>
            )}
            
            {isAuthenticated ? (
              <div className="pt-4 pb-3 border-t border-dark-700">
                <div className="flex items-center px-3">
                  {profile?.avatar_url ? (
                    <img
                      className="h-10 w-10 rounded-full ring-2 ring-primary-500/30"
                      src={profile.avatar_url}
                      alt={profile.first_name || 'User'}
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center">
                      <span className="text-white font-medium">
                        {profile?.first_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                      </span>
                    </div>
                  )}
                  <div className="ml-3">
                    <div className="text-base font-medium text-white">
                      {profile?.first_name} {profile?.last_name}
                    </div>
                    <div className="text-sm text-dark-400">
                      {profile?.role === 'admin' ? 'Administrador' : 
                       profile?.role === 'supervisor' ? 'Supervisor' : 'Trabajador'}
                    </div>
                  </div>
                </div>
                <div className="mt-3 px-2">
                  <button
                    onClick={handleSignOut}
                    className="block w-full text-left px-3 py-2 rounded-lg text-base font-medium text-dark-300 hover:text-white hover:bg-dark-800/50 transition-all duration-300"
                  >
                    Cerrar Sesión
                  </button>
                </div>
              </div>
            ) : (
              <div className="pt-4 pb-3 border-t border-dark-700 space-y-2 px-2">
                <Link
                  href="/login"
                  className="block w-full text-center px-3 py-2 rounded-lg text-base font-medium text-dark-300 hover:text-white hover:bg-dark-800/50 transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Iniciar Sesión
                </Link>
                <Link
                  href="/login"
                  className="block w-full text-center btn-luxury"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Acceder
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}

'use client'

import { useMemo } from 'react'
import type { Room, RoomStatus } from '@/lib/supabase'

interface RoomFiltersProps {
  rooms: Room[]
  selectedFloor: number | null
  selectedStatus: RoomStatus | null
  selectedType: string | null
  onFloorChange: (floor: number | null) => void
  onStatusChange: (status: RoomStatus | null) => void
  onTypeChange: (type: string | null) => void
  onClearFilters: () => void
}

export default function RoomFilters({
  rooms,
  selectedFloor,
  selectedStatus,
  selectedType,
  onFloorChange,
  onStatusChange,
  onTypeChange,
  onClearFilters
}: RoomFiltersProps) {
  const { floors, statuses, types } = useMemo(() => {
    const floorsSet = new Set<number>()
    const statusesSet = new Set<RoomStatus>()
    const typesSet = new Set<string>()

    rooms.forEach(room => {
      floorsSet.add(room.floor_number)
      statusesSet.add(room.status as RoomStatus)
      typesSet.add(room.room_type)
    })

    return {
      floors: Array.from(floorsSet).sort((a, b) => a - b),
      statuses: Array.from(statusesSet),
      types: Array.from(typesSet)
    }
  }, [rooms])

  const hasActiveFilters = selectedFloor !== null || selectedStatus !== null || selectedType !== null

  const statusLabels: Record<RoomStatus, string> = {
    libre: 'Libre',
    ocupado: 'Ocupado',
    reservado: 'Reservado',
    mantenimiento: 'Mantenimiento'
  }

  const typeLabels: Record<string, string> = {
    individual: 'Individual',
    doble: 'Doble',
    suite: 'Suite',
    familiar: 'Familiar',
    presidencial: 'Presidencial'
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2 sm:mb-0">
          Filtros
        </h3>
        
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Limpiar filtros
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {/* Filtro por piso */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Piso
          </label>
          <select
            value={selectedFloor || ''}
            onChange={(e) => onFloorChange(e.target.value ? Number(e.target.value) : null)}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Todos los pisos</option>
            {floors.map(floor => (
              <option key={floor} value={floor}>
                Piso {floor}
              </option>
            ))}
          </select>
        </div>

        {/* Filtro por estado */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Estado
          </label>
          <select
            value={selectedStatus || ''}
            onChange={(e) => onStatusChange(e.target.value as RoomStatus || null)}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Todos los estados</option>
            {statuses.map(status => (
              <option key={status} value={status}>
                {statusLabels[status]}
              </option>
            ))}
          </select>
        </div>

        {/* Filtro por tipo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo de habitación
          </label>
          <select
            value={selectedType || ''}
            onChange={(e) => onTypeChange(e.target.value || null)}
            className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          >
            <option value="">Todos los tipos</option>
            {types.map(type => (
              <option key={type} value={type}>
                {typeLabels[type] || type}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Filtros rápidos */}
      <div className="mt-6">
        <p className="text-sm font-medium text-gray-700 mb-3">Filtros rápidos:</p>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => onStatusChange('libre')}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedStatus === 'libre'
                ? 'bg-green-500 text-white'
                : 'bg-green-100 text-green-800 hover:bg-green-200'
            }`}
          >
            Solo libres
          </button>
          
          <button
            onClick={() => onStatusChange('ocupado')}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedStatus === 'ocupado'
                ? 'bg-red-500 text-white'
                : 'bg-red-100 text-red-800 hover:bg-red-200'
            }`}
          >
            Solo ocupadas
          </button>
          
          <button
            onClick={() => onStatusChange('mantenimiento')}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedStatus === 'mantenimiento'
                ? 'bg-orange-500 text-white'
                : 'bg-orange-100 text-orange-800 hover:bg-orange-200'
            }`}
          >
            En mantenimiento
          </button>
          
          <button
            onClick={() => onStatusChange('reservado')}
            className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
              selectedStatus === 'reservado'
                ? 'bg-blue-500 text-white'
                : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
            }`}
          >
            Reservadas
          </button>
        </div>
      </div>
    </div>
  )
}

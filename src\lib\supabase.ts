import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Tipos para la base de datos
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: number // Cambio a number para BIGSERIAL
          auth_user_id: string | null // Referencia opcional a auth.users
          email: string
          first_name: string
          second_name: string | null
          last_name: string
          second_last_name: string | null
          phone: string | null
          mobile_phone: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          document_type: string | null
          document_number: string | null
          birth_date: string | null
          nationality: string | null
          address: string | null
          city: string | null
          state: string | null
          postal_code: string | null
          country: string | null
          role: string
          job_position: string | null
          department: string | null
          hire_date: string | null
          salary: number | null
          employee_id: string | null
          is_active: boolean | null
          employment_status: string | null
          avatar_url: string | null
          notes: string | null
          work_schedule: any | null
          created_at: string
          updated_at: string
          created_by: number | null // Referencia a profiles.id
          updated_by: number | null // Referencia a profiles.id
        }
        Insert: {
          id?: number
          auth_user_id?: string | null
          email: string
          first_name: string
          second_name?: string | null
          last_name: string
          second_last_name?: string | null
          phone?: string | null
          mobile_phone?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          document_type?: string | null
          document_number?: string | null
          birth_date?: string | null
          nationality?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          role?: string
          job_position?: string | null
          department?: string | null
          hire_date?: string | null
          salary?: number | null
          employee_id?: string | null
          is_active?: boolean | null
          employment_status?: string | null
          avatar_url?: string | null
          notes?: string | null
          work_schedule?: any | null
          created_at?: string
          updated_at?: string
          created_by?: number | null
          updated_by?: number | null
        }
        Update: {
          id?: number
          auth_user_id?: string | null
          email?: string
          first_name?: string
          second_name?: string | null
          last_name?: string
          second_last_name?: string | null
          phone?: string | null
          mobile_phone?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          document_type?: string | null
          document_number?: string | null
          birth_date?: string | null
          nationality?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          postal_code?: string | null
          country?: string | null
          role?: string
          job_position?: string | null
          department?: string | null
          hire_date?: string | null
          salary?: number | null
          employee_id?: string | null
          is_active?: boolean | null
          employment_status?: string | null
          avatar_url?: string | null
          notes?: string | null
          work_schedule?: any | null
          updated_at?: string
          created_by?: number | null
          updated_by?: number | null
        }
      }
      departments: {
        Row: {
          id: string
          name: string
          description: string | null
          is_active: boolean | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          is_active?: boolean | null
          created_at?: string
        }
      }
      job_positions: {
        Row: {
          id: string
          name: string
          description: string | null
          department_id: string | null
          is_active: boolean | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          department_id?: string | null
          is_active?: boolean | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          department_id?: string | null
          is_active?: boolean | null
          created_at?: string
        }
      }
    }
    Views: {
      employee_view: {
        Row: {
          id: number
          auth_user_id: string | null
          email: string
          full_name: string
          first_name: string
          second_name: string | null
          last_name: string
          second_last_name: string | null
          phone: string | null
          mobile_phone: string | null
          document_type: string | null
          document_number: string | null
          birth_date: string | null
          nationality: string | null
          address: string | null
          city: string | null
          state: string | null
          postal_code: string | null
          country: string | null
          role: string
          job_position: string | null
          department: string | null
          employee_id: string | null
          hire_date: string | null
          salary: number | null
          is_active: boolean | null
          employment_status: string | null
          avatar_url: string | null
          notes: string | null
          work_schedule: any | null
          created_at: string
          updated_at: string
          created_by: number | null
          updated_by: number | null
        }
      }
    }
    Functions: {
      generate_employee_id: {
        Args: {}
        Returns: string
      }
      get_full_name: {
        Args: { profile_record: Database['public']['Tables']['profiles']['Row'] }
        Returns: string
      }
      get_profile_by_auth_id: {
        Args: { user_id: string }
        Returns: Database['public']['Tables']['profiles']['Row']
      }
      get_current_profile_id: {
        Args: {}
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Tipos de conveniencia para usar en la aplicación
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Department = Database['public']['Tables']['departments']['Row']
export type JobPosition = Database['public']['Tables']['job_positions']['Row']
export type EmployeeView = Database['public']['Views']['employee_view']['Row']

// Enums para mayor seguridad de tipos
export type UserRole = 'admin' | 'supervisor' | 'trabajador'
export type JobPositionType = 'gerente' | 'mucama' | 'guardia' | 'recepcionista' | 'bodeguero' | 'mantenimiento' | 'chef' | 'mesero' | 'contador'
export type DocumentType = 'cedula' | 'pasaporte' | 'licencia'
export type EmploymentStatus = 'activo' | 'inactivo' | 'suspendido' | 'vacaciones' | 'licencia'

// Crear cliente tipado
export const supabaseTyped = supabase as any

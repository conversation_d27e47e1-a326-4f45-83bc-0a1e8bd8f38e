import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Tipos para la base de datos
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: string
          updated_at?: string
        }
      }
      rooms: {
        Row: {
          id: string
          room_number: string
          room_type: string
          price_per_night: number
          is_available: boolean
          description: string | null
          amenities: string[] | null
          max_occupancy: number
          floor_number: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          room_number: string
          room_type: string
          price_per_night: number
          is_available?: boolean
          description?: string | null
          amenities?: string[] | null
          max_occupancy?: number
          floor_number?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          room_number?: string
          room_type?: string
          price_per_night?: number
          is_available?: boolean
          description?: string | null
          amenities?: string[] | null
          max_occupancy?: number
          floor_number?: number | null
          updated_at?: string
        }
      }
      bookings: {
        Row: {
          id: string
          room_id: string
          user_id: string
          check_in_date: string
          check_out_date: string
          total_price: number
          status: string
          guest_count: number
          special_requests: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          room_id: string
          user_id: string
          check_in_date: string
          check_out_date: string
          total_price: number
          status?: string
          guest_count?: number
          special_requests?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          room_id?: string
          user_id?: string
          check_in_date?: string
          check_out_date?: string
          total_price?: number
          status?: string
          guest_count?: number
          special_requests?: string | null
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Tipos de conveniencia para usar en la aplicación
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Room = Database['public']['Tables']['rooms']['Row']
export type Booking = Database['public']['Tables']['bookings']['Row']

// Crear cliente tipado
export const supabaseTyped = supabase as any // Puedes usar tipos más específicos si lo deseas

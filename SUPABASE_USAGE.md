# Guía de Uso de Supabase

Esta guía te muestra cómo usar los servicios de Supabase implementados en el proyecto.

## 🔐 Autenticación

### Registro de Usuario

```typescript
import { useAuth } from '@/contexts/AuthContext'

function RegisterForm() {
  const { signUp } = useAuth()

  const handleRegister = async (email: string, password: string, fullName: string) => {
    try {
      const result = await signUp(email, password, fullName)
      console.log('Usuario registrado:', result)
    } catch (error) {
      console.error('Error al registrar:', error.message)
    }
  }
}
```

### Inicio de Sesión

```typescript
import { useAuth } from '@/contexts/AuthContext'

function LoginForm() {
  const { signIn } = useAuth()

  const handleLogin = async (email: string, password: string) => {
    try {
      const result = await signIn(email, password)
      console.log('Usuario logueado:', result)
    } catch (error) {
      console.error('Error al iniciar sesión:', error.message)
    }
  }
}
```

### Verificar Estado de Autenticación

```typescript
import { useAuth } from '@/contexts/AuthContext'

function UserProfile() {
  const { user, profile, isAuthenticated, isAdmin, loading } = useAuth()

  if (loading) return <div>Cargando...</div>
  
  if (!isAuthenticated) return <div>No autenticado</div>

  return (
    <div>
      <h2>Perfil de Usuario</h2>
      <p>Email: {user?.email}</p>
      <p>Nombre: {profile?.full_name}</p>
      <p>Rol: {profile?.role}</p>
      {isAdmin && <p>Eres administrador</p>}
    </div>
  )
}
```

## 🏨 Gestión de Habitaciones

### Obtener Todas las Habitaciones

```typescript
import { roomsService } from '@/lib/supabase-utils'

async function getAllRooms() {
  try {
    const rooms = await roomsService.getAll()
    console.log('Habitaciones:', rooms)
    return rooms
  } catch (error) {
    console.error('Error al obtener habitaciones:', error)
  }
}
```

### Obtener Habitaciones Disponibles

```typescript
import { roomsService } from '@/lib/supabase-utils'

async function getAvailableRooms() {
  try {
    const availableRooms = await roomsService.getAvailable()
    console.log('Habitaciones disponibles:', availableRooms)
    return availableRooms
  } catch (error) {
    console.error('Error al obtener habitaciones disponibles:', error)
  }
}
```

### Crear Nueva Habitación (Solo Admins)

```typescript
import { roomsService } from '@/lib/supabase-utils'

async function createRoom() {
  try {
    const newRoom = await roomsService.create({
      room_number: '401',
      room_type: 'deluxe',
      price_per_night: 250.00,
      description: 'Habitación deluxe con vista al mar',
      amenities: ['wifi', 'tv', 'aire_acondicionado', 'minibar', 'balcon'],
      max_occupancy: 2,
      floor_number: 4,
      is_available: true
    })
    console.log('Habitación creada:', newRoom)
    return newRoom
  } catch (error) {
    console.error('Error al crear habitación:', error)
  }
}
```

## 📅 Gestión de Reservas

### Crear Nueva Reserva

```typescript
import { bookingsService, calculateTotalPrice } from '@/lib/supabase-utils'
import { useAuth } from '@/contexts/AuthContext'

function CreateBooking() {
  const { user } = useAuth()

  const createBooking = async (roomId: string, checkIn: string, checkOut: string, pricePerNight: number) => {
    if (!user) return

    try {
      const totalPrice = calculateTotalPrice(pricePerNight, checkIn, checkOut)
      
      const booking = await bookingsService.create({
        room_id: roomId,
        user_id: user.id,
        check_in_date: checkIn,
        check_out_date: checkOut,
        total_price: totalPrice,
        guest_count: 2,
        special_requests: 'Habitación en piso alto'
      })
      
      console.log('Reserva creada:', booking)
      return booking
    } catch (error) {
      console.error('Error al crear reserva:', error)
    }
  }
}
```

### Obtener Mis Reservas

```typescript
import { bookingsService } from '@/lib/supabase-utils'

async function getMyBookings() {
  try {
    const bookings = await bookingsService.getMyBookings()
    console.log('Mis reservas:', bookings)
    return bookings
  } catch (error) {
    console.error('Error al obtener reservas:', error)
  }
}
```

### Cancelar Reserva

```typescript
import { bookingsService } from '@/lib/supabase-utils'

async function cancelBooking(bookingId: string) {
  try {
    const cancelledBooking = await bookingsService.cancel(bookingId)
    console.log('Reserva cancelada:', cancelledBooking)
    return cancelledBooking
  } catch (error) {
    console.error('Error al cancelar reserva:', error)
  }
}
```

## 🔍 Verificar Disponibilidad

```typescript
import { checkRoomAvailability } from '@/lib/supabase-utils'

async function checkAvailability(roomId: string, checkIn: string, checkOut: string) {
  try {
    const isAvailable = await checkRoomAvailability(roomId, checkIn, checkOut)
    
    if (isAvailable) {
      console.log('Habitación disponible para las fechas seleccionadas')
    } else {
      console.log('Habitación no disponible para las fechas seleccionadas')
    }
    
    return isAvailable
  } catch (error) {
    console.error('Error al verificar disponibilidad:', error)
  }
}
```

## 🛡️ Protección de Rutas

### Ruta Protegida (Requiere Autenticación)

```typescript
import { ProtectedRoute } from '@/contexts/AuthContext'

function Dashboard() {
  return (
    <ProtectedRoute>
      <div>
        <h1>Dashboard</h1>
        <p>Solo usuarios autenticados pueden ver esto</p>
      </div>
    </ProtectedRoute>
  )
}
```

### Ruta de Admin (Requiere Permisos de Admin)

```typescript
import { AdminRoute } from '@/contexts/AuthContext'

function AdminPanel() {
  return (
    <AdminRoute>
      <div>
        <h1>Panel de Administración</h1>
        <p>Solo administradores pueden ver esto</p>
      </div>
    </AdminRoute>
  )
}
```

## 📊 Ejemplo de Componente Completo

```typescript
'use client'

import { useState, useEffect } from 'react'
import { roomsService, bookingsService } from '@/lib/supabase-utils'
import { useAuth } from '@/contexts/AuthContext'
import type { Room } from '@/lib/supabase'

function RoomsList() {
  const [rooms, setRooms] = useState<Room[]>([])
  const [loading, setLoading] = useState(true)
  const { isAuthenticated, user } = useAuth()

  useEffect(() => {
    loadRooms()
  }, [])

  const loadRooms = async () => {
    try {
      const roomsData = await roomsService.getAvailable()
      setRooms(roomsData)
    } catch (error) {
      console.error('Error al cargar habitaciones:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleBookRoom = async (roomId: string, pricePerNight: number) => {
    if (!isAuthenticated || !user) {
      alert('Debes iniciar sesión para hacer una reserva')
      return
    }

    try {
      const checkIn = '2024-01-15'
      const checkOut = '2024-01-17'
      
      const booking = await bookingsService.create({
        room_id: roomId,
        user_id: user.id,
        check_in_date: checkIn,
        check_out_date: checkOut,
        total_price: pricePerNight * 2, // 2 noches
        guest_count: 1
      })

      alert('Reserva creada exitosamente')
      console.log('Reserva:', booking)
    } catch (error) {
      console.error('Error al crear reserva:', error)
      alert('Error al crear la reserva')
    }
  }

  if (loading) return <div>Cargando habitaciones...</div>

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {rooms.map((room) => (
        <div key={room.id} className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold">Habitación {room.room_number}</h3>
          <p className="text-gray-600">Tipo: {room.room_type}</p>
          <p className="text-gray-600">Precio: ${room.price_per_night}/noche</p>
          <p className="text-gray-600">Capacidad: {room.max_occupancy} personas</p>
          
          {room.amenities && (
            <div className="mt-2">
              <p className="text-sm font-medium">Amenidades:</p>
              <div className="flex flex-wrap gap-1">
                {room.amenities.map((amenity, index) => (
                  <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                    {amenity}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          <button
            onClick={() => handleBookRoom(room.id, room.price_per_night)}
            className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
          >
            Reservar
          </button>
        </div>
      ))}
    </div>
  )
}

export default RoomsList
```

## 🔧 Consejos y Mejores Prácticas

1. **Manejo de Errores**: Siempre envuelve las llamadas a Supabase en try-catch
2. **Loading States**: Muestra indicadores de carga mientras se procesan las peticiones
3. **Validación**: Valida los datos antes de enviarlos a Supabase
4. **Tipos**: Usa los tipos TypeScript proporcionados para mayor seguridad
5. **RLS**: Las políticas de Row Level Security ya están configuradas, respeta los permisos
6. **Optimización**: Usa `select()` para obtener solo los campos necesarios

## 🚨 Notas Importantes

- Los usuarios solo pueden ver y modificar sus propias reservas
- Solo los administradores pueden gestionar habitaciones
- Las fechas deben estar en formato ISO (YYYY-MM-DD)
- Los precios se almacenan como DECIMAL(10,2)
- Las amenidades se almacenan como array de strings

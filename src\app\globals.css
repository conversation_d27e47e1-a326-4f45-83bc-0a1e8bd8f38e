@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-luxury text-dark-100 font-modern antialiased;
    background-attachment: fixed;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Botones de lujo */
  .btn-luxury {
    @apply px-6 py-3 bg-gradient-primary text-white font-medium rounded-xl shadow-luxury hover:shadow-glow transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500/30;
  }

  .btn-luxury-outline {
    @apply px-6 py-3 border-2 border-primary-600 text-primary-600 font-medium rounded-xl hover:bg-primary-600 hover:text-white transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-500/30;
  }

  .btn-gold {
    @apply px-6 py-3 bg-gradient-gold text-dark-900 font-medium rounded-xl shadow-luxury hover:shadow-glow-gold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-luxury-gold/30;
  }

  /* Tarjetas de lujo */
  .card-luxury {
    @apply bg-gradient-card backdrop-blur-luxury border border-dark-200/20 rounded-2xl shadow-luxury hover:shadow-card-hover transition-all duration-300 transform hover:scale-[1.02];
  }

  .card-dark {
    @apply bg-gradient-dark backdrop-blur-luxury border border-dark-600/30 rounded-2xl shadow-luxury text-dark-100;
  }

  /* Inputs de lujo */
  .input-luxury {
    @apply w-full px-4 py-3 bg-white/90 backdrop-blur-sm border border-dark-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-primary-500/30 focus:border-primary-500 transition-all duration-300 placeholder-dark-400;
  }

  /* Estados de habitaciones con estilo */
  .room-libre {
    @apply bg-gradient-to-br from-emerald-500 to-emerald-600 text-white shadow-lg hover:shadow-xl;
  }

  .room-ocupado {
    @apply bg-gradient-to-br from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl;
  }

  .room-reservado {
    @apply bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl;
  }

  .room-mantenimiento {
    @apply bg-gradient-to-br from-amber-500 to-amber-600 text-white shadow-lg hover:shadow-xl;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

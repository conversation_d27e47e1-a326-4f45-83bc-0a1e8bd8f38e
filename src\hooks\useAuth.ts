'use client'

import { useState, useEffect } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { authService, profilesService } from '@/lib/supabase-utils'
import type { Profile } from '@/lib/supabase'

interface AuthState {
  user: User | null
  profile: Profile | null
  session: Session | null
  loading: boolean
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    profile: null,
    session: null,
    loading: true
  })

  useEffect(() => {
    // Obtener sesión inicial
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session?.user) {
        try {
          const profile = await profilesService.getMyProfile()
          setAuthState({
            user: session.user,
            profile,
            session,
            loading: false
          })
        } catch (error) {
          console.error('Error loading profile:', error)
          setAuthState({
            user: session.user,
            profile: null,
            session,
            loading: false
          })
        }
      } else {
        setAuthState({
          user: null,
          profile: null,
          session: null,
          loading: false
        })
      }
    }

    getInitialSession()

    // Escuchar cambios de autenticación
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          try {
            const profile = await profilesService.getMyProfile()
            setAuthState({
              user: session.user,
              profile,
              session,
              loading: false
            })
          } catch (error) {
            console.error('Error loading profile:', error)
            setAuthState({
              user: session.user,
              profile: null,
              session,
              loading: false
            })
          }
        } else {
          setAuthState({
            user: null,
            profile: null,
            session: null,
            loading: false
          })
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (email: string, password: string, firstName?: string, lastName?: string) => {
    setAuthState(prev => ({ ...prev, loading: true }))
    try {
      const result = await authService.signUp(email, password, firstName, lastName)
      return result
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }))
      throw error
    }
  }

  const signIn = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true }))
    try {
      const result = await authService.signIn(email, password)
      return result
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }))
      throw error
    }
  }

  const signOut = async () => {
    setAuthState(prev => ({ ...prev, loading: true }))
    try {
      await authService.signOut()
    } catch (error) {
      setAuthState(prev => ({ ...prev, loading: false }))
      throw error
    }
  }

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      const updatedProfile = await profilesService.updateMyProfile(updates)
      setAuthState(prev => ({
        ...prev,
        profile: updatedProfile
      }))
      return updatedProfile
    } catch (error) {
      throw error
    }
  }

  const isAdmin = authState.profile?.role === 'admin'
  const isAuthenticated = !!authState.user

  return {
    ...authState,
    signUp,
    signIn,
    signOut,
    updateProfile,
    isAdmin,
    isAuthenticated
  }
}

'use client'

import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'

export default function Home() {
  const { user } = useAuth()

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-luxury"></div>

        {/* Efectos de fondo */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-luxury-gold/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            {/* Logo principal */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-gradient-primary rounded-3xl flex items-center justify-center shadow-luxury animate-float">
                <span className="text-white font-luxury font-bold text-3xl">H</span>
              </div>
            </div>

            <h1 className="text-5xl md:text-7xl font-luxury font-bold text-white mb-6">
              Hotel <span className="text-luxury">Luxury</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-dark-300 max-w-2xl mx-auto">
              Sistema de Gestión Hotelera de 5 Estrellas
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              {user ? (
                <Link
                  href="/dashboard"
                  className="btn-gold text-lg px-8 py-4 inline-flex items-center space-x-3"
                >
                  <span>🏨</span>
                  <span>Ir al Dashboard</span>
                </Link>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="btn-luxury text-lg px-8 py-4 inline-flex items-center space-x-3"
                  >
                    <span>🚀</span>
                    <span>Iniciar Sesión</span>
                  </Link>
                  <Link
                    href="/login"
                    className="btn-luxury-outline text-lg px-8 py-4 inline-flex items-center space-x-3"
                  >
                    <span>✨</span>
                    <span>Registrarse</span>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-dark-900 to-dark-800"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-luxury font-bold text-white mb-4">
              Características Premium
            </h2>
            <p className="text-xl text-dark-300 max-w-2xl mx-auto">
              Todo lo que necesitas para gestionar tu hotel de 5 estrellas
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="card-dark p-8 text-center hover-lift">
              <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow">
                <span className="text-white text-3xl">🏨</span>
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">
                Dashboard de Habitaciones
              </h3>
              <p className="text-dark-300">
                Monitorea el estado de todas las habitaciones en tiempo real con códigos de colores intuitivos
              </p>
            </div>

            <div className="card-dark p-8 text-center hover-lift">
              <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow">
                <span className="text-white text-3xl">📊</span>
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">
                Gestión de Estados
              </h3>
              <p className="text-dark-300">
                Cambia el estado de las habitaciones en tiempo real: Libre, Ocupado, Reservado, Mantenimiento
              </p>
            </div>

            <div className="card-dark p-8 text-center hover-lift">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-glow">
                <span className="text-white text-3xl">🔐</span>
              </div>
              <h3 className="text-2xl font-semibold text-white mb-4">
                Seguridad Premium
              </h3>
              <p className="text-dark-300">
                Autenticación segura con Google y control de acceso por roles de usuario
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

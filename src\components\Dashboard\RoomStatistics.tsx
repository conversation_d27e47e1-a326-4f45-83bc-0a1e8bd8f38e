'use client'

import { useMemo } from 'react'
import type { Room, RoomStatus } from '@/lib/supabase'

interface RoomStatisticsProps {
  rooms: Room[]
}

export default function RoomStatistics({ rooms }: RoomStatisticsProps) {
  const statistics = useMemo(() => {
    const stats = {
      total: rooms.length,
      libre: 0,
      ocupado: 0,
      reservado: 0,
      mantenimiento: 0
    }

    rooms.forEach(room => {
      const status = room.status as RoomStatus
      stats[status]++
    })

    return stats
  }, [rooms])

  const occupancyRate = statistics.total > 0 
    ? ((statistics.ocupado + statistics.reservado) / statistics.total * 100).toFixed(1)
    : '0'

  const availabilityRate = statistics.total > 0
    ? (statistics.libre / statistics.total * 100).toFixed(1)
    : '0'

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total de habitaciones */}
      <div className="card-luxury p-6 hover-lift">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-luxury rounded-xl flex items-center justify-center shadow-glow">
              <span className="text-white text-xl">🏨</span>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-dark-600">Total Habitaciones</p>
            <p className="text-3xl font-bold text-dark-900">{statistics.total}</p>
            <p className="text-xs text-dark-500">En el sistema</p>
          </div>
        </div>
      </div>

      {/* Habitaciones libres */}
      <div className="card-luxury p-6 hover-lift border-l-4 border-emerald-500">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-xl">✓</span>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-emerald-700">Habitaciones Libres</p>
            <p className="text-3xl font-bold text-emerald-600">{statistics.libre}</p>
            <p className="text-xs text-emerald-600 font-medium">{availabilityRate}% disponible</p>
          </div>
        </div>
      </div>

      {/* Habitaciones ocupadas */}
      <div className="card-luxury p-6 hover-lift border-l-4 border-red-500">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-xl">👤</span>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-red-700">Habitaciones Ocupadas</p>
            <p className="text-3xl font-bold text-red-600">{statistics.ocupado}</p>
            <p className="text-xs text-red-600 font-medium">{occupancyRate}% ocupación</p>
          </div>
        </div>
      </div>

      {/* Habitaciones en mantenimiento */}
      <div className="card-luxury p-6 hover-lift border-l-4 border-amber-500">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white text-xl">🔧</span>
            </div>
          </div>
          <div className="ml-4">
            <p className="text-sm font-medium text-amber-700">En Mantenimiento</p>
            <p className="text-3xl font-bold text-amber-600">{statistics.mantenimiento}</p>
            <p className="text-xs text-amber-600 font-medium">Fuera de servicio</p>
          </div>
        </div>
      </div>

      {/* Estadísticas adicionales */}
      <div className="bg-white rounded-lg shadow-sm border p-6 md:col-span-2 lg:col-span-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Resumen por Estado</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white font-bold">✓</span>
            </div>
            <p className="text-sm font-medium text-gray-900">{statistics.libre}</p>
            <p className="text-xs text-black">Libres</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white font-bold">👤</span>
            </div>
            <p className="text-sm font-medium text-gray-900">{statistics.ocupado}</p>
            <p className="text-xs text-black">Ocupadas</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white font-bold">📅</span>
            </div>
            <p className="text-sm font-medium text-gray-900">{statistics.reservado}</p>
            <p className="text-xs text-black">Reservadas</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white font-bold">🔧</span>
            </div>
            <p className="text-sm font-medium text-gray-900">{statistics.mantenimiento}</p>
            <p className="text-xs text-black">Mantenimiento</p>
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { userManagementService } from '@/lib/supabase-utils'
import type { UserRole } from '@/lib/supabase'

interface User {
  id: number
  email: string
  first_name: string
  last_name: string
  role: string
  created_at: string
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [changingRole, setChangingRole] = useState<number | null>(null)

  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      setLoading(true)
      const usersData = await userManagementService.getUsersForPromotion()
      setUsers(usersData)
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRoleChange = async (email: string, newRole: UserRole, userId: number) => {
    try {
      setChangingRole(userId)
      await userManagementService.changeUserRole(email, newRole)
      await loadUsers() // Recargar la lista
      alert(`Rol cambiado exitosamente a ${newRole}`)
    } catch (error: any) {
      console.error('Error changing role:', error)
      alert(`Error al cambiar rol: ${error.message}`)
    } finally {
      setChangingRole(null)
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800'
      case 'supervisor':
        return 'bg-yellow-100 text-yellow-800'
      case 'trabajador':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrador'
      case 'supervisor':
        return 'Supervisor'
      case 'trabajador':
        return 'Trabajador'
      default:
        return role
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Gestión de Usuarios
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Usuarios registrados que pueden ser promovidos a roles administrativos
        </p>
      </div>

      <div className="p-6">
        {users.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-4xl mb-4">👥</div>
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No hay usuarios para gestionar
            </h4>
            <p className="text-gray-500">
              Todos los usuarios registrados ya tienen roles administrativos.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {users.map((user) => (
              <div
                key={user.id}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {user.first_name} {user.last_name}
                      </h4>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                      {getRoleLabel(user.role)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    Registrado: {new Date(user.created_at).toLocaleDateString()}
                  </p>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleRoleChange(user.email, 'supervisor', user.id)}
                    disabled={changingRole === user.id}
                    className="px-3 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-md hover:bg-yellow-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {changingRole === user.id ? 'Cambiando...' : 'Hacer Supervisor'}
                  </button>
                  
                  <button
                    onClick={() => handleRoleChange(user.email, 'admin', user.id)}
                    disabled={changingRole === user.id}
                    className="px-3 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-md hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {changingRole === user.id ? 'Cambiando...' : 'Hacer Admin'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">
            ℹ️ Información sobre roles
          </h4>
          <div className="text-xs text-blue-800 space-y-1">
            <p><strong>Trabajador:</strong> Acceso básico al sistema</p>
            <p><strong>Supervisor:</strong> Puede gestionar habitaciones y ver reportes</p>
            <p><strong>Administrador:</strong> Acceso completo al sistema</p>
          </div>
        </div>
      </div>
    </div>
  )
}

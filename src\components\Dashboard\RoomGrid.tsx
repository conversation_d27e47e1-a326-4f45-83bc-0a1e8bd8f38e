'use client'

import { useState } from 'react'
import type { Room, RoomStatus } from '@/lib/supabase'
import RoomCard from './RoomCard'
import EnhancedRoomStatusModal from './EnhancedRoomStatusModal'

interface RoomGridProps {
  rooms: Room[]
  onRoomsChange: () => void
}

export default function RoomGrid({ rooms, onRoomsChange }: RoomGridProps) {
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleRoomClick = (room: Room) => {
    setSelectedRoom(room)
    setIsModalOpen(true)
  }

  const handleStatusChanged = () => {
    onRoomsChange()
    closeModal()
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedRoom(null)
  }

  // Agrupar habitaciones por piso
  const roomsByFloor = rooms.reduce((acc, room) => {
    const floor = room.floor_number
    if (!acc[floor]) {
      acc[floor] = []
    }
    acc[floor].push(room)
    return acc
  }, {} as Record<number, Room[]>)

  const floors = Object.keys(roomsByFloor)
    .map(Number)
    .sort((a, b) => a - b)

  if (rooms.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-6xl mb-4">🏨</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No hay habitaciones
        </h3>
        <p className="text-gray-500">
          No se encontraron habitaciones con los filtros aplicados.
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="space-y-8">
        {floors.map(floor => (
          <div key={floor} className="border-b border-dark-200 pb-8 last:border-b-0">
            <h3 className="text-xl font-luxury font-bold text-dark-900 mb-6 flex items-center">
              <span className="mr-2">🏢</span>
              Piso {floor}
            </h3>
            
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
              {roomsByFloor[floor]
                .sort((a, b) => a.room_number.localeCompare(b.room_number))
                .map(room => (
                  <RoomCard
                    key={room.id}
                    room={room}
                    onClick={() => handleRoomClick(room)}
                  />
                ))
              }
            </div>
          </div>
        ))}
      </div>

      {/* Modal mejorado para gestión de habitaciones */}
      <EnhancedRoomStatusModal
        room={selectedRoom}
        isOpen={isModalOpen}
        onClose={closeModal}
        onStatusChanged={handleStatusChanged}
      />
    </>
  )
}

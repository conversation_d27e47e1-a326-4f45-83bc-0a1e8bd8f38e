'use client'

import { useState } from 'react'
import type { Room, RoomStatus } from '@/lib/supabase'

interface RoomCardProps {
  room: Room
  onClick: (action?: 'status' | 'edit') => void
}

const statusColors: Record<RoomStatus, string> = {
  libre: 'room-libre border-emerald-400',
  ocupado: 'room-ocupado border-red-400',
  reservado: 'room-reservado border-blue-400',
  mantenimiento: 'room-mantenimiento border-amber-400'
}

const statusIcons: Record<RoomStatus, string> = {
  libre: '✓',
  ocupado: '👤',
  reservado: '📅',
  mantenimiento: '🔧'
}

const statusLabels: Record<RoomStatus, string> = {
  libre: 'Libre',
  ocupado: 'Ocupado',
  reservado: 'Reservado',
  mantenimiento: 'Mantenimiento'
}

const roomTypeLabels: Record<string, string> = {
  individual: 'Individual',
  doble: 'Doble',
  suite: 'Suite',
  familiar: 'Familiar',
  presidencial: 'Presidencial'
}

export default function RoomCard({ room, onClick }: RoomCardProps) {
  const [showMenu, setShowMenu] = useState(false)
  const colorClass = statusColors[room.status as RoomStatus]
  const icon = statusIcons[room.status as RoomStatus]
  const statusLabel = statusLabels[room.status as RoomStatus]
  const typeLabel = roomTypeLabels[room.room_type] || room.room_type

  return (
    <div className="w-full">
      {/* Tarjeta principal */}
      <button
        onClick={onClick}
        className={`
          relative w-full aspect-square rounded-2xl border-2 text-white font-semibold
          transition-all duration-300 transform hover:scale-105 hover:shadow-luxury
          focus:outline-none focus:ring-4 focus:ring-primary-500/30
          backdrop-blur-sm overflow-hidden group
          ${colorClass}
        `}
      >
        {/* Efecto de brillo en hover */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Número de habitación - Esquina superior izquierda */}
        <div className="absolute top-4 left-4 text-2xl font-bold drop-shadow-lg">
          {room.room_number}
        </div>

        {/* Icono de estado - Esquina superior derecha */}
        <div className="absolute top-4 right-4 text-2xl drop-shadow-lg">
          {icon}
        </div>

        {/* Menú contextual */}
        <div className="absolute top-2 right-2">
          <button
            onClick={(e) => {
              e.stopPropagation()
              setShowMenu(!showMenu)
            }}
            className="w-6 h-6 bg-black/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white/80 hover:text-white hover:bg-black/30 transition-all"
          >
            ⋮
          </button>

          {showMenu && (
            <div className="absolute top-8 right-0 bg-white rounded-lg shadow-luxury border border-gray-200 py-1 z-10 min-w-[120px]">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onClick('status')
                  setShowMenu(false)
                }}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
              >
                <span>🔄</span>
                <span>Cambiar Estado</span>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onClick('edit')
                  setShowMenu(false)
                }}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
              >
                <span>✏️</span>
                <span>Editar</span>
              </button>
            </div>
          )}
        </div>

        {/* Información central - Solo estado y tipo */}
        <div className="flex flex-col items-center justify-center h-full px-4 relative z-10">
          <div className="text-xl font-bold mb-2 drop-shadow-lg text-center">
            {statusLabel}
          </div>
          <div className="text-base opacity-95 font-medium text-center">
            {typeLabel}
          </div>
        </div>

        {/* Información adicional en esquina inferior */}
        <div className="absolute bottom-4 left-4 right-4 flex justify-between items-end">
          {/* Capacidad */}
          <div className="bg-white/20 backdrop-blur-sm rounded-lg px-2 py-1">
            <div className="text-xs font-bold flex items-center">
              <span className="mr-1">👤</span>
              <span>{room.capacity}</span>
            </div>
          </div>

          {/* Piso */}
          <div className="bg-white/20 backdrop-blur-sm rounded-lg px-2 py-1">
            <div className="text-xs font-bold flex items-center">
              <span className="mr-1">🏢</span>
              <span>{room.floor_number}</span>
            </div>
          </div>
        </div>

        {/* Indicador de amenidades - Centrado arriba */}
        {room.amenities && room.amenities.length > 0 && (
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-1">
              {room.amenities.slice(0, 3).map((_, index) => (
                <div key={index} className="w-2 h-2 bg-white/70 rounded-full"></div>
              ))}
            </div>
          </div>
        )}
      </button>

      {/* Precio separado abajo con fondo negro */}
      <div className="mt-2 bg-black text-white rounded-lg px-3 py-2 text-center">
        <div className="text-sm font-bold">
          ${room.price_per_night.toLocaleString()}
        </div>
        <div className="text-xs opacity-80">
          por noche
        </div>
      </div>
    </div>
  )
}

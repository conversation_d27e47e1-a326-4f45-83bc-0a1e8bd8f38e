'use client'

import type { Room, RoomStatus } from '@/lib/supabase'

interface RoomCardProps {
  room: Room
  onClick: () => void
}

const statusColors: Record<RoomStatus, string> = {
  libre: 'room-libre border-emerald-400',
  ocupado: 'room-ocupado border-red-400',
  reservado: 'room-reservado border-blue-400',
  mantenimiento: 'room-mantenimiento border-amber-400'
}

const statusIcons: Record<RoomStatus, string> = {
  libre: '✓',
  ocupado: '👤',
  reservado: '📅',
  mantenimiento: '🔧'
}

const statusLabels: Record<RoomStatus, string> = {
  libre: 'Libre',
  ocupado: 'Ocupado',
  reservado: 'Reservado',
  mantenimiento: 'Mantenimiento'
}

const roomTypeLabels: Record<string, string> = {
  individual: 'Individual',
  doble: 'Doble',
  suite: 'Suite',
  familiar: 'Familiar',
  presidencial: 'Presidencial'
}

export default function RoomCard({ room, onClick }: RoomCardProps) {
  const colorClass = statusColors[room.status as RoomStatus]
  const icon = statusIcons[room.status as RoomStatus]
  const statusLabel = statusLabels[room.status as RoomStatus]
  const typeLabel = roomTypeLabels[room.room_type] || room.room_type

  return (
    <button
      onClick={onClick}
      className={`
        relative w-full aspect-square rounded-2xl border-2 text-white font-semibold
        transition-all duration-300 transform hover:scale-105 hover:shadow-luxury
        focus:outline-none focus:ring-4 focus:ring-primary-500/30
        backdrop-blur-sm overflow-hidden group
        ${colorClass}
      `}
    >
      {/* Efecto de brillo en hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      {/* Número de habitación */}
      <div className="absolute top-3 left-3 text-xl font-bold drop-shadow-lg">
        {room.room_number}
      </div>

      {/* Icono de estado */}
      <div className="absolute top-3 right-3 text-xl drop-shadow-lg">
        {icon}
      </div>

      {/* Información de capacidad - Posición superior visible */}
      <div className="absolute top-3 right-12 bg-white/20 backdrop-blur-sm rounded-full px-2 py-1">
        <div className="text-xs font-bold flex items-center">
          <span className="mr-1">👤</span>
          <span>{room.capacity}</span>
        </div>
      </div>

      {/* Información central */}
      <div className="flex flex-col items-center justify-center h-full px-3 relative z-10">
        <div className="text-base font-bold mb-2 drop-shadow-lg">
          {statusLabel}
        </div>
        <div className="text-sm opacity-95 font-medium">
          {typeLabel}
        </div>
      </div>

      {/* Precio con estilo moderno */}
      <div className="absolute bottom-3 left-3 right-3">
        <div className="bg-black/30 backdrop-blur-sm rounded-lg px-2 py-1 text-center">
          <div className="text-sm font-bold">
            ${room.price_per_night.toLocaleString()}
          </div>
          <div className="text-xs opacity-90">
            por noche
          </div>
        </div>
      </div>

      {/* Indicador de amenidades mejorado */}
      {room.amenities && room.amenities.length > 0 && (
        <div className="absolute top-3 left-1/2 transform -translate-x-1/2">
          <div className="flex space-x-1">
            {room.amenities.slice(0, 3).map((_, index) => (
              <div key={index} className="w-1.5 h-1.5 bg-white/70 rounded-full"></div>
            ))}
          </div>
        </div>
      )}

      {/* Piso indicator - Movido a la esquina superior izquierda */}
      <div className="absolute top-3 left-12 bg-white/20 backdrop-blur-sm rounded-full px-2 py-1">
        <div className="text-xs font-bold flex items-center">
          <span className="mr-1">🏢</span>
          <span>{room.floor_number}</span>
        </div>
      </div>
    </button>
  )
}

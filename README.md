# Hotel Management System

Sistema de gestión de habitaciones para hoteles construido con Next.js, Supabase y autenticación con Google.

## 🚀 Características

- ✅ Autenticación con Google OAuth
- ✅ Gestión de usuarios con Supabase
- ✅ Interfaz moderna con Tailwind CSS
- ✅ TypeScript para mayor seguridad
- 🔄 Gestión de habitaciones (próximamente)
- 🔄 Sistema de reservas (próximamente)

## 📋 Requisitos Previos

- Node.js 18+
- npm o yarn
- Cuenta de Supabase
- Proyecto de Google Cloud Console (para OAuth)

## 🛠️ Configuración

### 1. Configurar Supabase

1. Ve a [supabase.com](https://supabase.com) y crea una cuenta
2. Crea un nuevo proyecto
3. Ve a Settings > API y copia:
   - Project URL
   - Anon public key

### 2. Configurar Google OAuth

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la Google+ API
4. Ve a "Credentials" > "Create Credentials" > "OAuth 2.0 Client IDs"
5. Configura las URLs autorizadas:
   - JavaScript origins: `http://localhost:3000`
   - Redirect URIs: `https://[tu-proyecto].supabase.co/auth/v1/callback`

### 3. Configurar Supabase Auth

1. En tu proyecto de Supabase, ve a Authentication > Providers
2. Habilita Google
3. Ingresa tu Google Client ID y Client Secret
4. Guarda los cambios

### 4. Variables de Entorno

Completa el archivo `.env.local` con tus datos:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://tu-proyecto.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_anon_key_aqui
```

### 5. Ejecutar el Proyecto

```bash
npm run dev
```

Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

## 📁 Estructura del Proyecto

```
hotel-management/
├── src/
│   ├── app/                 # App Router de Next.js
│   │   ├── dashboard/       # Página del dashboard
│   │   ├── login/          # Página de login
│   │   └── layout.tsx      # Layout principal
│   ├── components/         # Componentes reutilizables
│   │   ├── Auth/           # Componentes de autenticación
│   │   └── Navigation/     # Componentes de navegación
│   ├── contexts/           # Contextos de React
│   │   └── AuthContext.tsx # Contexto de autenticación
│   └── lib/                # Utilidades y configuración
│       └── supabase.ts     # Cliente de Supabase
├── .env.local              # Variables de entorno
└── README.md
```

## 🔧 Próximos Pasos

1. **Crear tablas en Supabase:**
   - Tabla `rooms` para habitaciones
   - Tabla `bookings` para reservas
   - Tabla `profiles` para perfiles de usuario

2. **Implementar funcionalidades:**
   - CRUD de habitaciones
   - Sistema de reservas
   - Dashboard con estadísticas
   - Gestión de usuarios

## 📚 Tecnologías Utilizadas

- **Frontend:** Next.js 15, React 18, TypeScript
- **Styling:** Tailwind CSS
- **Backend:** Supabase (PostgreSQL)
- **Autenticación:** Supabase Auth + Google OAuth
- **Deployment:** Vercel (recomendado)

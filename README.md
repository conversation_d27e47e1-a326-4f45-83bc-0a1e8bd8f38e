# Hotel Management System

Sistema de gestión de habitaciones para hoteles construido con Next.js, Supabase y autenticación con Google.

## 🚀 Características

- ✅ **Sistema de Gestión de Empleados Completo**
  - Perfiles detallados con información personal y laboral
  - Roles y permisos (Admin, Supervisor, Trabajador)
  - Posiciones de trabajo y departamentos
  - Gestión de horarios y estados de empleo
- ✅ **Autenticación y Seguridad**
  - Autenticación completa con Supabase Auth
  - Row Level Security (RLS) implementado
  - Políticas de acceso basadas en roles
- ✅ **Tecnología Moderna**
  - Next.js 15 con App Router
  - TypeScript para mayor seguridad
  - Tailwind CSS para diseño responsive
  - Hooks y contextos personalizados
  - Componentes de protección de rutas

## 📋 Requisitos Previos

- Node.js 18+
- npm o yarn
- Cuenta de Supabase
- Proyecto de Google Cloud Console (para OAuth)

## 🛠️ Configuración

### 1. Configurar Supabase

1. Ve a [supabase.com](https://supabase.com) y crea una cuenta
2. Crea un nuevo proyecto
3. Ve a Settings > API y copia:
   - Project URL
   - Anon public key

### 2. Configurar Google OAuth

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la Google+ API
4. Ve a "Credentials" > "Create Credentials" > "OAuth 2.0 Client IDs"
5. Configura las URLs autorizadas:
   - JavaScript origins: `http://localhost:3000`
   - Redirect URIs: `https://[tu-proyecto].supabase.co/auth/v1/callback`

### 3. Configurar Supabase Auth

1. En tu proyecto de Supabase, ve a Authentication > Providers
2. Habilita Google
3. Ingresa tu Google Client ID y Client Secret
4. Guarda los cambios

### 4. Variables de Entorno

El archivo `.env.local` ya está configurado con las credenciales de Supabase:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://rgkkgaaqfcpwryyguclk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Nota:** Las tablas de la base de datos ya están creadas y configuradas.

### 5. Ejecutar el Proyecto

```bash
npm run dev
```

Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

## 📁 Estructura del Proyecto

```
hotel-management/
├── src/
│   ├── app/                 # App Router de Next.js
│   │   ├── dashboard/       # Página del dashboard
│   │   ├── login/          # Página de login
│   │   └── layout.tsx      # Layout principal
│   ├── components/         # Componentes reutilizables
│   │   ├── Auth/           # Componentes de autenticación
│   │   └── Navigation/     # Componentes de navegación
│   ├── contexts/           # Contextos de React
│   │   └── AuthContext.tsx # Contexto de autenticación
│   ├── hooks/              # Hooks personalizados
│   │   └── useAuth.ts      # Hook de autenticación
│   └── lib/                # Utilidades y configuración
│       ├── supabase.ts     # Cliente de Supabase con tipos
│       └── supabase-utils.ts # Funciones de utilidad
├── .env.local              # Variables de entorno
└── README.md
```

## �️ Base de Datos

Las siguientes tablas ya están creadas y configuradas en Supabase:

### Tablas Principales

1. **`profiles`** - Empleados del Hotel (Arquitectura Mejorada)
   - **ID Autoincremental**: BIGSERIAL independiente de autenticación
   - **Referencia Opcional**: Enlace flexible a `auth.users` (permite empleados sin cuenta)
   - **Información Personal**: Nombres completos, fecha de nacimiento, nacionalidad
   - **Contacto**: Teléfonos, dirección, contacto de emergencia
   - **Identificación**: Tipo y número de documento
   - **Información Laboral**: Rol, posición, departamento, salario, fecha de contratación
   - **Estado**: Activo/inactivo, estado de empleo, horarios de trabajo
   - **Auditoría**: Referencias internas para seguimiento de cambios

2. **`departments`** - Departamentos del Hotel
   - Administración, Recepción, Housekeeping, Seguridad
   - Almacén, Mantenimiento, Restaurante, Contabilidad

3. **`job_positions`** - Posiciones de Trabajo
   - Gerente, Recepcionista, Mucama, Guardia
   - Bodeguero, Técnico, Chef, Mesero, Contador
   - Relación con departamentos

### Seguridad (RLS)

- **Row Level Security** habilitado en todas las tablas
- **Roles de Usuario**: Admin, Supervisor, Trabajador
- **Arquitectura Desacoplada**: Separación entre autenticación y datos de negocio
- **Políticas de Acceso**:
  - Empleados solo ven su propio perfil
  - Supervisores gestionan empleados de su área
  - Administradores tienen acceso completo
- **Auditoría Mejorada**: Seguimiento por ID de empleado (no por auth)
- **Flexibilidad**: Empleados pueden existir sin cuenta de usuario

## 🚀 Funcionalidades Implementadas

### Gestión de Empleados
- **CRUD Completo**: Crear, leer, actualizar empleados
- **Búsqueda Avanzada**: Por nombre, documento, departamento
- **Gestión de Roles**: Cambio de permisos y posiciones
- **Estados de Empleo**: Activo, inactivo, suspendido, vacaciones
- **ID Automático**: Generación de códigos de empleado (EMP001, EMP002...)

### Servicios de Datos
- `employeeService` - Gestión completa de empleados
- `departmentService` - Administración de departamentos
- `jobPositionService` - Gestión de posiciones de trabajo
- `profilesService` - Perfil del usuario actual
- `authService` - Operaciones de autenticación
- `utilityService` - Funciones de utilidad y validación

### Funciones Especiales
- **Vista `employee_view`**: Consulta optimizada con nombres completos
- **Función `generate_employee_id()`**: IDs únicos automáticos (EMP001, EMP002...)
- **Función `get_current_profile_id()`**: Obtiene ID del perfil actual
- **Función `get_profile_by_auth_id()`**: Busca perfil por UUID de auth
- **Validación de documentos**: Cédula, pasaporte, licencia
- **Horarios flexibles**: Sistema JSON para turnos rotativos
- **Auditoría mejorada**: Seguimiento por empleado, no por auth

## 📚 Tecnologías Utilizadas

- **Frontend:** Next.js 15, React 18, TypeScript
- **Styling:** Tailwind CSS
- **Backend:** Supabase (PostgreSQL)
- **Autenticación:** Supabase Auth + Google OAuth
- **Deployment:** Vercel (recomendado)

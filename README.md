# Hotel Management System

Sistema de gestión de habitaciones para hoteles construido con Next.js, Supabase y autenticación con Google.

## 🚀 Características

- ✅ Autenticación completa con Supabase Auth
- ✅ Gestión de usuarios con perfiles y roles
- ✅ Sistema de habitaciones completo
- ✅ Sistema de reservas funcional
- ✅ Row Level Security (RLS) implementado
- ✅ Interfaz moderna con Tailwind CSS
- ✅ TypeScript para mayor seguridad
- ✅ Hooks y contextos personalizados
- ✅ Componentes de protección de rutas

## 📋 Requisitos Previos

- Node.js 18+
- npm o yarn
- Cuenta de Supabase
- Proyecto de Google Cloud Console (para OAuth)

## 🛠️ Configuración

### 1. Configurar Supabase

1. Ve a [supabase.com](https://supabase.com) y crea una cuenta
2. Crea un nuevo proyecto
3. Ve a Settings > API y copia:
   - Project URL
   - Anon public key

### 2. Configurar Google OAuth

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la Google+ API
4. Ve a "Credentials" > "Create Credentials" > "OAuth 2.0 Client IDs"
5. Configura las URLs autorizadas:
   - JavaScript origins: `http://localhost:3000`
   - Redirect URIs: `https://[tu-proyecto].supabase.co/auth/v1/callback`

### 3. Configurar Supabase Auth

1. En tu proyecto de Supabase, ve a Authentication > Providers
2. Habilita Google
3. Ingresa tu Google Client ID y Client Secret
4. Guarda los cambios

### 4. Variables de Entorno

El archivo `.env.local` ya está configurado con las credenciales de Supabase:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://rgkkgaaqfcpwryyguclk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Nota:** Las tablas de la base de datos ya están creadas y configuradas.

### 5. Ejecutar el Proyecto

```bash
npm run dev
```

Abre [http://localhost:3000](http://localhost:3000) en tu navegador.

## 📁 Estructura del Proyecto

```
hotel-management/
├── src/
│   ├── app/                 # App Router de Next.js
│   │   ├── dashboard/       # Página del dashboard
│   │   ├── login/          # Página de login
│   │   └── layout.tsx      # Layout principal
│   ├── components/         # Componentes reutilizables
│   │   ├── Auth/           # Componentes de autenticación
│   │   └── Navigation/     # Componentes de navegación
│   ├── contexts/           # Contextos de React
│   │   └── AuthContext.tsx # Contexto de autenticación
│   ├── hooks/              # Hooks personalizados
│   │   └── useAuth.ts      # Hook de autenticación
│   └── lib/                # Utilidades y configuración
│       ├── supabase.ts     # Cliente de Supabase con tipos
│       └── supabase-utils.ts # Funciones de utilidad
├── .env.local              # Variables de entorno
└── README.md
```

## �️ Base de Datos

Las siguientes tablas ya están creadas y configuradas en Supabase:

### Tablas Principales

1. **`profiles`** - Perfiles de usuario
   - Gestión de roles (guest/admin)
   - Información personal del usuario
   - Creación automática al registrarse

2. **`rooms`** - Habitaciones del hotel
   - Tipos: single, double, suite, deluxe
   - Precios, amenidades y disponibilidad
   - Datos de ejemplo ya insertados

3. **`bookings`** - Reservas
   - Relación con usuarios y habitaciones
   - Fechas de check-in/check-out
   - Estados: pending, confirmed, cancelled, completed

### Seguridad (RLS)

- **Row Level Security** habilitado en todas las tablas
- Políticas de acceso basadas en roles
- Los usuarios solo pueden ver sus propias reservas
- Solo admins pueden gestionar habitaciones

## 🚀 Funcionalidades Implementadas

### Autenticación
- Registro e inicio de sesión con email/contraseña
- Gestión automática de perfiles
- Contexto de autenticación global
- Componentes de protección de rutas

### Servicios de Datos
- `roomsService` - CRUD completo de habitaciones
- `bookingsService` - Gestión de reservas
- `profilesService` - Gestión de perfiles
- `authService` - Operaciones de autenticación

### Utilidades
- Verificación de disponibilidad de habitaciones
- Cálculo automático de precios
- Hooks personalizados para autenticación
- Tipos TypeScript completos

## 📚 Tecnologías Utilizadas

- **Frontend:** Next.js 15, React 18, TypeScript
- **Styling:** Tailwind CSS
- **Backend:** Supabase (PostgreSQL)
- **Autenticación:** Supabase Auth + Google OAuth
- **Deployment:** Vercel (recomendado)

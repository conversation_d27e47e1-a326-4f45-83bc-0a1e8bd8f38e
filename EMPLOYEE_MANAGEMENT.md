# Sistema de Gestión de Empleados

Este documento describe el sistema completo de gestión de empleados implementado en el proyecto de gestión hotelera.

## 📋 Estructura de la Base de Datos

### Tabla `profiles` (Empleados)

La tabla principal que almacena toda la información de los empleados:

#### Información Personal
- `first_name` - Primer nombre (requerido)
- `second_name` - <PERSON><PERSON><PERSON> nombre (opcional)
- `last_name` - Primer <PERSON><PERSON><PERSON><PERSON> (requerido)
- `second_last_name` - <PERSON><PERSON><PERSON> (opcional)
- `birth_date` - Fecha de nacimiento
- `nationality` - Nacionalidad

#### Información de Contacto
- `email` - Correo electrónico (único, requerido)
- `phone` - Teléfono fijo
- `mobile_phone` - Teléfono móvil
- `emergency_contact_name` - Nombre del contacto de emergencia
- `emergency_contact_phone` - Teléfono del contacto de emergencia

#### Información de Identificación
- `document_type` - Tipo de documento (cédula, pasaporte, licencia)
- `document_number` - Número de documento (único)

#### Dirección
- `address` - Dirección completa
- `city` - Ciudad
- `state` - Departamento/Estado
- `postal_code` - Código postal
- `country` - País (por defecto: Colombia)

#### Información Laboral
- `role` - Rol del empleado (admin, supervisor, trabajador)
- `job_position` - Posición específica (gerente, mucama, guardia, etc.)
- `department` - Departamento al que pertenece
- `hire_date` - Fecha de contratación
- `salary` - Salario (decimal)
- `employee_id` - ID único del empleado (generado automáticamente)

#### Estado del Empleado
- `is_active` - Si el empleado está activo
- `employment_status` - Estado específico (activo, inactivo, suspendido, vacaciones, licencia)

#### Información Adicional
- `avatar_url` - URL de la foto del empleado
- `notes` - Notas adicionales
- `work_schedule` - Horario de trabajo (JSON flexible)

### Tablas de Soporte

#### `departments` - Departamentos
- `id` - UUID único
- `name` - Nombre del departamento
- `description` - Descripción
- `is_active` - Si está activo

#### `job_positions` - Posiciones de Trabajo
- `id` - UUID único
- `name` - Nombre de la posición
- `description` - Descripción
- `department_id` - Referencia al departamento
- `is_active` - Si está activa

## 🔐 Seguridad y Permisos

### Roles de Usuario
1. **admin** - Acceso completo al sistema
2. **supervisor** - Puede gestionar empleados de su departamento
3. **trabajador** - Solo puede ver y editar su propio perfil

### Políticas de Seguridad (RLS)
- Los usuarios solo pueden ver su propio perfil
- Los administradores pueden ver y gestionar todos los perfiles
- Los supervisores pueden gestionar perfiles de empleados
- Solo administradores pueden crear/eliminar empleados

## 🛠️ Funciones Disponibles

### `employeeService`

#### Consultas
```typescript
// Obtener todos los empleados (admin/supervisor)
const employees = await employeeService.getAll()

// Obtener empleados activos
const activeEmployees = await employeeService.getActive()

// Obtener empleado por ID
const employee = await employeeService.getById(id)

// Obtener empleados por departamento
const deptEmployees = await employeeService.getByDepartment('Recepción')

// Buscar empleados por nombre
const results = await employeeService.searchByName('Juan')

// Buscar por número de documento
const employee = await employeeService.searchByDocument('12345678')
```

#### Gestión
```typescript
// Crear nuevo empleado (solo admin)
const newEmployee = await employeeService.create({
  email: '<EMAIL>',
  first_name: 'Juan',
  last_name: 'Pérez',
  role: 'trabajador',
  job_position: 'recepcionista',
  department: 'Recepción'
})

// Actualizar empleado
const updated = await employeeService.update(id, {
  phone: '+57 ************',
  salary: 1500000
})

// Cambiar rol (solo admin)
await employeeService.changeRole(id, 'supervisor')

// Desactivar empleado
await employeeService.deactivate(id)

// Reactivar empleado
await employeeService.reactivate(id)
```

### `departmentService`

```typescript
// Obtener todos los departamentos
const departments = await departmentService.getAll()

// Crear departamento (solo admin)
const newDept = await departmentService.create({
  name: 'Nuevo Departamento',
  description: 'Descripción del departamento'
})
```

### `jobPositionService`

```typescript
// Obtener todas las posiciones
const positions = await jobPositionService.getAll()

// Obtener posiciones por departamento
const deptPositions = await jobPositionService.getByDepartment(departmentId)
```

## 🔧 Funciones de Utilidad

### `utilityService`

```typescript
// Obtener nombre completo
const fullName = utilityService.getFullName(profile)

// Calcular edad
const age = utilityService.calculateAge('1990-05-15')

// Formatear teléfono
const formatted = utilityService.formatPhoneNumber('3001234567')

// Validar documento
const isValid = utilityService.validateDocument('cedula', '12345678')

// Generar horario por defecto
const schedule = utilityService.generateDefaultSchedule('recepcionista')
```

## 📊 Vistas y Funciones de Base de Datos

### Vista `employee_view`
Proporciona una vista optimizada con nombre completo calculado:

```sql
SELECT * FROM employee_view WHERE is_active = true;
```

### Función `generate_employee_id()`
Genera automáticamente IDs únicos para empleados (EMP001, EMP002, etc.):

```sql
SELECT generate_employee_id();
```

### Función `get_full_name()`
Concatena todos los nombres del empleado:

```sql
SELECT get_full_name(profiles.*) FROM profiles;
```

## 📝 Ejemplos de Uso

### Registro de Nuevo Empleado

```typescript
import { employeeService } from '@/lib/supabase-utils'

const createEmployee = async () => {
  try {
    const newEmployee = await employeeService.create({
      email: '<EMAIL>',
      first_name: 'María',
      second_name: 'Elena',
      last_name: 'García',
      second_last_name: 'López',
      phone: '+57 1 234 5678',
      mobile_phone: '+57 ************',
      document_type: 'cedula',
      document_number: '12345678',
      birth_date: '1985-03-20',
      nationality: 'Colombiana',
      address: 'Calle 123 #45-67',
      city: 'Bogotá',
      state: 'Cundinamarca',
      country: 'Colombia',
      role: 'trabajador',
      job_position: 'mucama',
      department: 'Housekeeping',
      hire_date: '2024-01-15',
      salary: 1200000,
      emergency_contact_name: 'Pedro García',
      emergency_contact_phone: '+57 ************'
    })
    
    console.log('Empleado creado:', newEmployee)
  } catch (error) {
    console.error('Error:', error.message)
  }
}
```

### Búsqueda y Filtrado

```typescript
import { employeeService } from '@/lib/supabase-utils'

const searchEmployees = async () => {
  // Buscar por nombre
  const byName = await employeeService.searchByName('María')
  
  // Filtrar por departamento
  const housekeeping = await employeeService.getByDepartment('Housekeeping')
  
  // Filtrar por posición
  const receptionists = await employeeService.getByJobPosition('recepcionista')
  
  console.log('Resultados:', { byName, housekeeping, receptionists })
}
```

### Actualización de Perfil

```typescript
import { employeeService } from '@/lib/supabase-utils'

const updateEmployee = async (employeeId: string) => {
  try {
    const updated = await employeeService.update(employeeId, {
      phone: '+57 1 987 6543',
      address: 'Nueva dirección 456',
      salary: 1350000,
      employment_status: 'activo',
      notes: 'Empleado destacado del mes'
    })
    
    console.log('Empleado actualizado:', updated)
  } catch (error) {
    console.error('Error:', error.message)
  }
}
```

## 🚨 Consideraciones Importantes

1. **Privacidad**: Los datos sensibles como salarios solo son visibles para administradores
2. **Validación**: Siempre validar datos antes de insertar/actualizar
3. **Auditoría**: Los campos `created_by` y `updated_by` rastrean cambios
4. **Soft Delete**: Usar `is_active = false` en lugar de eliminar registros
5. **Documentos**: Los números de documento deben ser únicos
6. **Horarios**: El campo `work_schedule` permite horarios flexibles en formato JSON

## 🔄 Próximas Mejoras

- [ ] Sistema de permisos granulares
- [ ] Historial de cambios de empleados
- [ ] Gestión de nómina
- [ ] Sistema de evaluaciones
- [ ] Gestión de vacaciones y licencias
- [ ] Reportes y estadísticas avanzadas

'use client'

import { useState, useEffect } from 'react'
import { roomService } from '@/lib/supabase-utils'
import type { Room } from '@/lib/supabase'

interface FloorManagementProps {
  rooms: Room[]
  onRoomsChange: () => void
}

interface FloorInfo {
  floor_number: number
  room_count: number
  rooms: Room[]
  total_capacity: number
  occupied_rooms: number
  available_rooms: number
}

export default function FloorManagement({ rooms, onRoomsChange }: FloorManagementProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [newFloorNumber, setNewFloorNumber] = useState('')
  const [roomsPerFloor, setRoomsPerFloor] = useState(10)
  const [startingRoomNumber, setStartingRoomNumber] = useState('')

  // Analizar pisos existentes
  const floorAnalysis: FloorInfo[] = rooms.reduce((acc, room) => {
    const existingFloor = acc.find(f => f.floor_number === room.floor_number)
    
    if (existingFloor) {
      existingFloor.room_count++
      existingFloor.rooms.push(room)
      existingFloor.total_capacity += room.capacity
      if (room.status === 'ocupado') existingFloor.occupied_rooms++
      if (room.status === 'libre') existingFloor.available_rooms++
    } else {
      acc.push({
        floor_number: room.floor_number,
        room_count: 1,
        rooms: [room],
        total_capacity: room.capacity,
        occupied_rooms: room.status === 'ocupado' ? 1 : 0,
        available_rooms: room.status === 'libre' ? 1 : 0
      })
    }
    
    return acc
  }, [] as FloorInfo[]).sort((a, b) => a.floor_number - b.floor_number)

  const handleCreateFloor = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const floorNum = parseInt(newFloorNumber)
      
      // Verificar que el piso no exista
      const existingFloor = floorAnalysis.find(f => f.floor_number === floorNum)
      if (existingFloor) {
        alert(`El piso ${floorNum} ya existe con ${existingFloor.room_count} habitaciones.`)
        return
      }

      // Generar habitaciones para el nuevo piso
      const roomsToCreate = []
      for (let i = 1; i <= roomsPerFloor; i++) {
        const roomNumber = startingRoomNumber 
          ? `${startingRoomNumber}${i.toString().padStart(2, '0')}`
          : `${floorNum}${i.toString().padStart(2, '0')}`

        roomsToCreate.push({
          room_number: roomNumber,
          floor_number: floorNum,
          room_type: 'individual' as const,
          capacity: 2,
          price_per_night: 80000,
          description: `Habitación ${roomNumber} - Piso ${floorNum}`,
          amenities: ['wifi', 'tv', 'aire_acondicionado'],
          status: 'libre' as const,
          is_active: true
        })
      }

      // Crear todas las habitaciones
      for (const roomData of roomsToCreate) {
        await roomService.create(roomData)
      }

      onRoomsChange()
      setIsOpen(false)
      setNewFloorNumber('')
      setStartingRoomNumber('')
      setRoomsPerFloor(10)
      
      alert(`Piso ${floorNum} creado exitosamente con ${roomsPerFloor} habitaciones.`)
    } catch (error) {
      console.error('Error al crear piso:', error)
      alert('Error al crear el piso. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteFloor = async (floorInfo: FloorInfo) => {
    const hasOccupiedRooms = floorInfo.occupied_rooms > 0
    
    if (hasOccupiedRooms) {
      alert(`No se puede eliminar el piso ${floorInfo.floor_number} porque tiene ${floorInfo.occupied_rooms} habitaciones ocupadas.`)
      return
    }

    const confirmMessage = `¿Estás seguro de que quieres eliminar el piso ${floorInfo.floor_number}?\n\nEsto eliminará ${floorInfo.room_count} habitaciones permanentemente.`
    
    if (!confirm(confirmMessage)) {
      return
    }

    setIsLoading(true)
    try {
      // Eliminar todas las habitaciones del piso
      for (const room of floorInfo.rooms) {
        await roomService.delete(room.id)
      }
      
      onRoomsChange()
      alert(`Piso ${floorInfo.floor_number} eliminado exitosamente.`)
    } catch (error) {
      console.error('Error al eliminar piso:', error)
      alert('Error al eliminar el piso. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      {/* Botón para abrir gestión de pisos */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="btn-secondary flex items-center space-x-2"
        >
          <span>🏢</span>
          <span>Gestionar Pisos</span>
        </button>
      )}

      {/* Modal de gestión de pisos */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-luxury max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-luxury font-bold text-dark-900">
                  🏢 Gestión de Pisos
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-dark-600 hover:text-dark-800 text-2xl"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* Resumen de pisos existentes */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-dark-900 mb-4">
                  📊 Pisos Existentes
                </h3>
                
                {floorAnalysis.length === 0 ? (
                  <div className="text-center py-8 text-dark-600">
                    <span className="text-4xl mb-4 block">🏢</span>
                    <p>No hay pisos creados aún</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {floorAnalysis.map(floor => (
                      <div key={floor.floor_number} className="card-luxury p-4">
                        <div className="flex justify-between items-start mb-3">
                          <h4 className="text-lg font-semibold text-dark-900">
                            Piso {floor.floor_number}
                          </h4>
                          <button
                            onClick={() => handleDeleteFloor(floor)}
                            className="text-red-500 hover:text-red-700 text-sm"
                            disabled={isLoading || floor.occupied_rooms > 0}
                            title={floor.occupied_rooms > 0 ? 'No se puede eliminar: hay habitaciones ocupadas' : 'Eliminar piso'}
                          >
                            🗑️
                          </button>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-dark-700">Habitaciones:</span>
                            <span className="font-medium">{floor.room_count}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-dark-700">Capacidad total:</span>
                            <span className="font-medium">{floor.total_capacity} personas</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-dark-700">Ocupadas:</span>
                            <span className="font-medium text-red-600">{floor.occupied_rooms}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-dark-700">Disponibles:</span>
                            <span className="font-medium text-green-600">{floor.available_rooms}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Formulario para crear nuevo piso */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-4">
                  ➕ Crear Nuevo Piso
                </h3>
                
                <form onSubmit={handleCreateFloor} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-dark-800 mb-2">
                        Número de Piso
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="50"
                        value={newFloorNumber}
                        onChange={(e) => setNewFloorNumber(e.target.value)}
                        className="input-luxury"
                        placeholder="Ej: 3"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-dark-800 mb-2">
                        Habitaciones por Piso
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="50"
                        value={roomsPerFloor}
                        onChange={(e) => setRoomsPerFloor(parseInt(e.target.value))}
                        className="input-luxury"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-dark-800 mb-2">
                        Prefijo de Habitación (Opcional)
                      </label>
                      <input
                        type="text"
                        value={startingRoomNumber}
                        onChange={(e) => setStartingRoomNumber(e.target.value)}
                        className="input-luxury"
                        placeholder="Ej: 3 (para 301, 302...)"
                      />
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-medium text-blue-900 mb-2">Vista Previa:</h4>
                    <p className="text-sm text-blue-700">
                      Se crearán {roomsPerFloor} habitaciones en el piso {newFloorNumber || 'X'}
                      {newFloorNumber && (
                        <>
                          <br />
                          Números: {startingRoomNumber || newFloorNumber}01 - {startingRoomNumber || newFloorNumber}{roomsPerFloor.toString().padStart(2, '0')}
                        </>
                      )}
                    </p>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={() => setIsOpen(false)}
                      className="btn-secondary"
                      disabled={isLoading}
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      className="btn-primary"
                      disabled={isLoading || !newFloorNumber}
                    >
                      {isLoading ? 'Creando...' : 'Crear Piso'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

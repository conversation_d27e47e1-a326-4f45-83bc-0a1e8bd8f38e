'use client'

import { useState } from 'react'
import type { Room, RoomStatus } from '@/lib/supabase'

interface RoomStatusModalProps {
  room: Room
  isOpen: boolean
  onClose: () => void
  onStatusChange: (newStatus: RoomStatus, reason?: string) => Promise<void>
}

const statusOptions: { value: RoomStatus; label: string; color: string; icon: string }[] = [
  { value: 'libre', label: 'Libre', color: 'bg-green-500', icon: '✓' },
  { value: 'ocupado', label: 'Ocupado', color: 'bg-red-500', icon: '👤' },
  { value: 'reservado', label: 'Reservado', color: 'bg-blue-500', icon: '📅' },
  { value: 'mantenimiento', label: 'Mantenimiento', color: 'bg-orange-500', icon: '🔧' }
]

const typeLabels: Record<string, string> = {
  individual: 'Individual',
  doble: 'Doble',
  suite: 'Suite',
  familiar: 'Familiar',
  presidencial: 'Presidencial'
}

export default function RoomStatusModal({ room, isOpen, onClose, onStatusChange }: RoomStatusModalProps) {
  const [selectedStatus, setSelectedStatus] = useState<RoomStatus>(room.status as RoomStatus)
  const [reason, setReason] = useState('')
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (selectedStatus === room.status && !reason.trim()) {
      onClose()
      return
    }

    setLoading(true)
    try {
      await onStatusChange(selectedStatus, reason.trim() || undefined)
    } catch (error) {
      console.error('Error changing status:', error)
    } finally {
      setLoading(false)
    }
  }

  const currentStatusOption = statusOptions.find(option => option.value === room.status)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Habitación {room.room_number}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <span className="sr-only">Cerrar</span>
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="px-6 py-4">
          {/* Información de la habitación */}
          <div className="mb-6">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Tipo:</span>
                <span className="ml-2 text-gray-900">{typeLabels[room.room_type] || room.room_type}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Capacidad:</span>
                <span className="ml-2 text-gray-900">{room.capacity} personas</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Piso:</span>
                <span className="ml-2 text-gray-900">{room.floor_number}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Precio:</span>
                <span className="ml-2 text-gray-900">${room.price_per_night.toLocaleString()}/noche</span>
              </div>
            </div>

            {room.description && (
              <div className="mt-3">
                <span className="font-medium text-gray-700">Descripción:</span>
                <p className="mt-1 text-sm text-gray-600">{room.description}</p>
              </div>
            )}

            {room.amenities && room.amenities.length > 0 && (
              <div className="mt-3">
                <span className="font-medium text-gray-700">Amenidades:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {room.amenities.map((amenity, index) => (
                    <span
                      key={index}
                      className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                    >
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Estado actual */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estado actual
            </label>
            <div className="flex items-center">
              <div className={`w-4 h-4 ${currentStatusOption?.color} rounded mr-3`}></div>
              <span className="text-sm text-gray-900">
                {currentStatusOption?.label} {currentStatusOption?.icon}
              </span>
            </div>
          </div>

          {/* Nuevo estado */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Cambiar estado a:
            </label>
            <div className="grid grid-cols-2 gap-3">
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => setSelectedStatus(option.value)}
                  className={`
                    p-3 rounded-lg border-2 transition-all duration-200
                    ${selectedStatus === option.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <div className="flex items-center justify-center mb-2">
                    <div className={`w-6 h-6 ${option.color} rounded flex items-center justify-center text-white text-sm`}>
                      {option.icon}
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {option.label}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Razón del cambio */}
          <div className="mb-6">
            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
              Razón del cambio (opcional)
            </label>
            <textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe el motivo del cambio de estado..."
            />
          </div>

          {/* Botones */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Guardando...' : 'Guardar cambios'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

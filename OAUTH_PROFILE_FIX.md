# Solución: Creación Automática de Perfiles para OAuth

## 🚨 Problema Identificado

Cuando los usuarios se autentican con **Google OAuth**, el trigger `handle_new_user()` no siempre se ejecuta correctamente, resultando en usuarios autenticados que no tienen un perfil en la tabla `profiles`.

### **Síntomas:**
- Usuario puede hacer login con Google
- Usuario aparece en `auth.users` 
- Usuario NO aparece en `profiles`
- Error al acceder al dashboard o funciones que requieren perfil

## 🔧 Solución Implementada

### **1. Función `ensure_user_profile()`**
Función robusta que garantiza que cualquier usuario autenticado tenga un perfil:

```sql
CREATE OR REPLACE FUNCTION ensure_user_profile()
RETURNS profiles AS $$
DECLARE
    user_record auth.users;
    profile_record profiles;
BEGIN
    -- Obtener el usuario actual
    SELECT * INTO user_record FROM auth.users WHERE id = auth.uid();
    
    -- Si no hay usuario autenticado, retornar null
    IF user_record.id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- <PERSON><PERSON> si ya existe un perfil
    SELECT * INTO profile_record FROM profiles WHERE auth_user_id = user_record.id;
    
    -- Si no existe perfil, crearlo
    IF profile_record.id IS NULL THEN
        INSERT INTO profiles (
            auth_user_id, email, first_name, last_name, 
            avatar_url, role, country
        ) VALUES (
            user_record.id, user_record.email,
            COALESCE(
                user_record.raw_user_meta_data->>'first_name',
                user_record.raw_user_meta_data->>'name',
                split_part(user_record.raw_user_meta_data->>'full_name', ' ', 1),
                'Usuario'
            ),
            COALESCE(
                user_record.raw_user_meta_data->>'last_name',
                split_part(user_record.raw_user_meta_data->>'full_name', ' ', 2),
                'Nuevo'
            ),
            user_record.raw_user_meta_data->>'avatar_url',
            'trabajador', 'Colombia'
        ) RETURNING * INTO profile_record;
    END IF;
    
    RETURN profile_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **2. Actualización de `get_current_profile_id()`**
Ahora usa `ensure_user_profile()` para garantizar que siempre retorne un ID válido:

```sql
CREATE OR REPLACE FUNCTION get_current_profile_id()
RETURNS BIGINT AS $$
DECLARE
    profile_record profiles;
BEGIN
    SELECT * INTO profile_record FROM ensure_user_profile();
    RETURN profile_record.id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **3. Mejora del Servicio `profilesService.getMyProfile()`**
Ahora crea automáticamente el perfil si no existe:

```typescript
async getMyProfile() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    // Intentar obtener el perfil existente
    let { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()
    
    // Si no existe, usar la función para crearlo automáticamente
    if (error && error.code === 'PGRST116') {
      const { data: newProfile, error: createError } = await supabase
        .rpc('ensure_user_profile')
      
      if (createError) throw createError
      return newProfile as Profile
    }
    
    if (error) throw error
    return data as Profile
}
```

### **4. Trigger Mejorado `handle_new_user()`**
Más robusto para manejar diferentes tipos de autenticación:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Solo crear perfil si no existe ya uno
    IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE auth_user_id = NEW.id) THEN
        INSERT INTO public.profiles (
            auth_user_id, email, first_name, last_name,
            avatar_url, role, country
        ) VALUES (
            NEW.id, NEW.email,
            COALESCE(
                NEW.raw_user_meta_data->>'first_name',
                NEW.raw_user_meta_data->>'name',
                split_part(NEW.raw_user_meta_data->>'full_name', ' ', 1),
                'Usuario'
            ),
            COALESCE(
                NEW.raw_user_meta_data->>'last_name',
                split_part(NEW.raw_user_meta_data->>'full_name', ' ', 2),
                'Nuevo'
            ),
            NEW.raw_user_meta_data->>'avatar_url',
            'trabajador', 'Colombia'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🛠️ Funciones de Gestión de Usuarios

### **Cambiar Roles de Usuario**
```sql
-- Función para cambiar roles fácilmente
CREATE OR REPLACE FUNCTION change_user_role(user_email TEXT, new_role TEXT)
RETURNS profiles AS $$
DECLARE
    profile_record profiles;
BEGIN
    IF new_role NOT IN ('admin', 'supervisor', 'trabajador') THEN
        RAISE EXCEPTION 'Rol inválido: %. Roles válidos: admin, supervisor, trabajador', new_role;
    END IF;
    
    UPDATE profiles 
    SET role = new_role, updated_at = NOW()
    WHERE email = user_email
    RETURNING * INTO profile_record;
    
    IF profile_record.id IS NULL THEN
        RAISE EXCEPTION 'Usuario no encontrado con email: %', user_email;
    END IF;
    
    RETURN profile_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### **Obtener Usuarios para Promoción**
```sql
CREATE OR REPLACE FUNCTION get_users_for_promotion()
RETURNS TABLE(
    id BIGINT, email TEXT, first_name TEXT, 
    last_name TEXT, role TEXT, created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT p.id, p.email, p.first_name, p.last_name, p.role, p.created_at
    FROM profiles p
    WHERE p.role = 'trabajador' AND p.auth_user_id IS NOT NULL
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🎯 Componentes de Gestión de Usuarios

### **UserManagement Component**
- Lista usuarios que pueden ser promovidos
- Botones para cambiar roles (Supervisor/Admin)
- Interfaz intuitiva con colores por rol
- Manejo de estados de carga

### **Dashboard de Usuarios**
- Página dedicada en `/dashboard/users`
- Solo accesible por administradores
- Instrucciones claras de uso
- Estadísticas de usuarios por rol

## ✅ Resultado Final

### **Flujo Automático:**
1. Usuario se autentica con Google OAuth
2. Si no tiene perfil → se crea automáticamente
3. Rol por defecto: `trabajador`
4. Admin puede promover a `supervisor` o `admin`
5. Acceso al dashboard según rol

### **Beneficios:**
- ✅ **Sin errores**: Todos los usuarios autenticados tienen perfil
- ✅ **Automático**: No requiere intervención manual
- ✅ **Robusto**: Maneja diferentes tipos de autenticación
- ✅ **Escalable**: Funciona con cualquier proveedor OAuth
- ✅ **Auditable**: Todos los cambios se registran

### **Gestión Simplificada:**
- ✅ Interfaz web para cambiar roles
- ✅ Validaciones automáticas
- ✅ Mensajes de error claros
- ✅ Actualizaciones en tiempo real

## 🚀 Uso Práctico

### **Para Administradores:**
1. Ir a `/dashboard/users`
2. Ver lista de usuarios registrados
3. Promover usuarios con botones simples
4. Cambios se aplican inmediatamente

### **Para Desarrolladores:**
```typescript
// Asegurar que el usuario actual tiene perfil
const profile = await userManagementService.ensureCurrentUserProfile()

// Cambiar rol de usuario
await userManagementService.changeUserRole('<EMAIL>', 'admin')

// Obtener usuarios para promoción
const users = await userManagementService.getUsersForPromotion()
```

## 🔄 Casos de Prueba

### **Escenario 1: Nuevo usuario OAuth**
1. Usuario se registra con Google
2. Sistema crea perfil automáticamente
3. Usuario puede acceder a funciones básicas

### **Escenario 2: Usuario existente sin perfil**
1. Usuario existe en `auth.users` pero no en `profiles`
2. Al acceder a cualquier función → perfil se crea automáticamente
3. No hay errores ni interrupciones

### **Escenario 3: Promoción de usuario**
1. Admin accede a `/dashboard/users`
2. Ve lista de usuarios con rol `trabajador`
3. Hace click en "Hacer Admin"
4. Usuario inmediatamente puede acceder al dashboard

¡El sistema ahora es completamente robusto y maneja todos los casos de autenticación OAuth sin problemas!

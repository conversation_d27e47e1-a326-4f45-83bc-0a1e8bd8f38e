-- Hotel Management System Database Schema - Employee Management
-- Eje<PERSON>a este script en el SQL Editor de Supabase

-- <PERSON>rear tabla de perfiles completa para empleados
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,

    -- Información personal básica
    first_name TEXT NOT NULL,
    second_name TEXT,
    last_name TEXT NOT NULL,
    second_last_name TEXT,

    -- Información de contacto
    phone TEXT,
    mobile_phone TEXT,
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,

    -- Información de identificación
    document_type TEXT CHECK (document_type IN ('cedula', 'pasaporte', 'licencia')),
    document_number TEXT UNIQUE,
    birth_date DATE,
    nationality TEXT,

    -- Dirección
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'Colombia',

    -- Información laboral
    role TEXT NOT NULL DEFAULT 'trabajador' CHECK (role IN ('admin', 'supervisor', 'trabajador')),
    job_position TEXT CHECK (job_position IN ('gerente', 'mucama', 'guardia', 'recepcionista', 'bodeguero', 'mantenimiento', 'chef', 'mesero', 'contador')),
    department TEXT,
    hire_date DATE,
    salary DECIMAL(10,2),
    employee_id TEXT UNIQUE,

    -- Estado del empleado
    is_active BOOLEAN DEFAULT true,
    employment_status TEXT DEFAULT 'activo' CHECK (employment_status IN ('activo', 'inactivo', 'suspendido', 'vacaciones', 'licencia')),

    -- Información adicional
    avatar_url TEXT,
    notes TEXT,

    -- Horario de trabajo
    work_schedule JSONB, -- Para almacenar horarios flexibles

    -- Metadatos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Crear tabla de departamentos
CREATE TABLE IF NOT EXISTS departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de posiciones de trabajo
CREATE TABLE IF NOT EXISTS job_positions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    department_id UUID REFERENCES departments(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_document_number ON profiles(document_number);
CREATE INDEX IF NOT EXISTS idx_profiles_employee_id ON profiles(employee_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_job_position ON profiles(job_position);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_employment_status ON profiles(employment_status);
CREATE INDEX IF NOT EXISTS idx_profiles_full_name ON profiles(first_name, last_name);

-- Crear triggers para actualizar updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Crear función mejorada para crear perfil automáticamente al registrarse
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        first_name,
        last_name,
        avatar_url,
        role,
        country
    )
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', split_part(NEW.raw_user_meta_data->>'full_name', ' ', 1), 'Usuario'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', split_part(NEW.raw_user_meta_data->>'full_name', ' ', 2), 'Nuevo'),
        NEW.raw_user_meta_data->>'avatar_url',
        'trabajador', -- Rol por defecto
        'Colombia' -- País por defecto
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Crear trigger para crear perfil automáticamente
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Funciones de utilidad
-- Función para generar ID de empleado automáticamente
CREATE OR REPLACE FUNCTION generate_employee_id()
RETURNS TEXT AS $$
DECLARE
    new_id TEXT;
    counter INTEGER;
BEGIN
    -- Obtener el siguiente número secuencial
    SELECT COALESCE(MAX(CAST(SUBSTRING(employee_id FROM 4) AS INTEGER)), 0) + 1
    INTO counter
    FROM profiles
    WHERE employee_id IS NOT NULL AND employee_id ~ '^EMP[0-9]+$';

    -- Formatear como EMP001, EMP002, etc.
    new_id := 'EMP' || LPAD(counter::TEXT, 3, '0');

    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Función para obtener nombre completo
CREATE OR REPLACE FUNCTION get_full_name(profile_record profiles)
RETURNS TEXT AS $$
BEGIN
    RETURN TRIM(
        COALESCE(profile_record.first_name, '') || ' ' ||
        COALESCE(profile_record.second_name, '') || ' ' ||
        COALESCE(profile_record.last_name, '') || ' ' ||
        COALESCE(profile_record.second_last_name, '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Vista para obtener información de empleados con nombre completo
CREATE OR REPLACE VIEW employee_view AS
SELECT
    id,
    email,
    get_full_name(profiles.*) as full_name,
    first_name,
    second_name,
    last_name,
    second_last_name,
    phone,
    mobile_phone,
    document_type,
    document_number,
    role,
    job_position,
    department,
    employee_id,
    hire_date,
    is_active,
    employment_status,
    created_at,
    updated_at
FROM profiles
WHERE is_active = true
ORDER BY last_name, first_name;

-- Habilitar Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_positions ENABLE ROW LEVEL SECURITY;

-- Políticas de seguridad para profiles
-- Los usuarios pueden ver su propio perfil
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

-- Los usuarios pueden actualizar su propio perfil (campos limitados)
CREATE POLICY "Users can update their own basic profile" ON profiles
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        -- Solo pueden actualizar ciertos campos, no el rol ni información laboral crítica
        auth.uid() = id
    );

-- Los administradores pueden ver todos los perfiles
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Los administradores y supervisores pueden actualizar perfiles de empleados
CREATE POLICY "Admins and supervisors can update employee profiles" ON profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Solo los administradores pueden crear nuevos perfiles manualmente
CREATE POLICY "Admins can insert profiles" ON profiles
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Solo los administradores pueden eliminar perfiles
CREATE POLICY "Admins can delete profiles" ON profiles
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Políticas para departments
CREATE POLICY "Everyone can view departments" ON departments
    FOR SELECT USING (true);

CREATE POLICY "Only admins can manage departments" ON departments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Políticas para job_positions
CREATE POLICY "Everyone can view job positions" ON job_positions
    FOR SELECT USING (true);

CREATE POLICY "Only admins can manage job positions" ON job_positions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Insertar datos de ejemplo para departamentos
INSERT INTO departments (name, description) VALUES
('Administración', 'Gestión general del hotel'),
('Recepción', 'Atención al cliente y check-in/check-out'),
('Housekeeping', 'Limpieza y mantenimiento de habitaciones'),
('Seguridad', 'Vigilancia y protección del hotel'),
('Almacén', 'Gestión de inventarios y suministros'),
('Mantenimiento', 'Reparaciones y mantenimiento de instalaciones'),
('Restaurante', 'Servicios de alimentación'),
('Contabilidad', 'Gestión financiera y contable');

-- Insertar posiciones de trabajo
INSERT INTO job_positions (name, description, department_id) VALUES
('gerente', 'Gerente General', (SELECT id FROM departments WHERE name = 'Administración')),
('recepcionista', 'Recepcionista', (SELECT id FROM departments WHERE name = 'Recepción')),
('mucama', 'Mucama/Camarera', (SELECT id FROM departments WHERE name = 'Housekeeping')),
('guardia', 'Guardia de Seguridad', (SELECT id FROM departments WHERE name = 'Seguridad')),
('bodeguero', 'Encargado de Bodega', (SELECT id FROM departments WHERE name = 'Almacén')),
('mantenimiento', 'Técnico de Mantenimiento', (SELECT id FROM departments WHERE name = 'Mantenimiento')),
('chef', 'Chef/Cocinero', (SELECT id FROM departments WHERE name = 'Restaurante')),
('mesero', 'Mesero/Camarero', (SELECT id FROM departments WHERE name = 'Restaurante')),
('contador', 'Contador', (SELECT id FROM departments WHERE name = 'Contabilidad'));

-- Crear un usuario admin de ejemplo (opcional)
-- Nota: Esto debe hacerse después de que un usuario se registre por primera vez
-- UPDATE profiles SET role = 'admin', job_position = 'gerente', department = 'Administración', employee_id = 'EMP001' WHERE email = '<EMAIL>';

-- Hotel Management System Database Schema - Employee Management
-- Eje<PERSON>a este script en el SQL Editor de Supabase

-- <PERSON>rear tabla de perfiles con ID autoincremental
CREATE TABLE IF NOT EXISTS profiles (
    id BIGSERIAL PRIMARY KEY, -- ID autoincremental propio
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- Referencia opcional a auth
    email TEXT UNIQUE NOT NULL,

    -- Información personal básica
    first_name TEXT NOT NULL,
    second_name TEXT,
    last_name TEXT NOT NULL,
    second_last_name TEXT,

    -- Información de contacto
    phone TEXT,
    mobile_phone TEXT,
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,

    -- Información de identificación
    document_type TEXT CHECK (document_type IN ('cedula', 'pasaporte', 'licencia')),
    document_number TEXT UNIQUE,
    birth_date DATE,
    nationality TEXT,

    -- Dirección
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'Colombia',

    -- Información laboral
    role TEXT NOT NULL DEFAULT 'trabajador' CHECK (role IN ('admin', 'supervisor', 'trabajador')),
    job_position TEXT CHECK (job_position IN ('gerente', 'mucama', 'guardia', 'recepcionista', 'bodeguero', 'mantenimiento', 'chef', 'mesero', 'contador')),
    department TEXT,
    hire_date DATE,
    salary DECIMAL(10,2),
    employee_id TEXT UNIQUE,

    -- Estado del empleado
    is_active BOOLEAN DEFAULT true,
    employment_status TEXT DEFAULT 'activo' CHECK (employment_status IN ('activo', 'inactivo', 'suspendido', 'vacaciones', 'licencia')),

    -- Información adicional
    avatar_url TEXT,
    notes TEXT,

    -- Horario de trabajo
    work_schedule JSONB,

    -- Metadatos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by BIGINT REFERENCES profiles(id), -- Referencia a profiles, no auth
    updated_by BIGINT REFERENCES profiles(id)  -- Referencia a profiles, no auth
);

-- Crear tabla de departamentos
CREATE TABLE IF NOT EXISTS departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear tabla de posiciones de trabajo
CREATE TABLE IF NOT EXISTS job_positions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    department_id UUID REFERENCES departments(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Crear función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_profiles_auth_user_id ON profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_document_number ON profiles(document_number);
CREATE INDEX IF NOT EXISTS idx_profiles_employee_id ON profiles(employee_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_job_position ON profiles(job_position);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_employment_status ON profiles(employment_status);
CREATE INDEX IF NOT EXISTS idx_profiles_full_name ON profiles(first_name, last_name);
CREATE INDEX IF NOT EXISTS idx_profiles_department ON profiles(department);

-- Crear triggers para actualizar updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Crear función mejorada para crear perfil automáticamente al registrarse
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        auth_user_id,
        email,
        first_name,
        last_name,
        avatar_url,
        role,
        country
    )
    VALUES (
        NEW.id, -- Referencia al auth.users.id
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'first_name', split_part(NEW.raw_user_meta_data->>'full_name', ' ', 1), 'Usuario'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', split_part(NEW.raw_user_meta_data->>'full_name', ' ', 2), 'Nuevo'),
        NEW.raw_user_meta_data->>'avatar_url',
        'trabajador', -- Rol por defecto
        'Colombia' -- País por defecto
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Crear trigger para crear perfil automáticamente
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Funciones de utilidad
-- Función para generar ID de empleado automáticamente
CREATE OR REPLACE FUNCTION generate_employee_id()
RETURNS TEXT AS $$
DECLARE
    new_id TEXT;
    counter INTEGER;
BEGIN
    -- Obtener el siguiente número secuencial
    SELECT COALESCE(MAX(CAST(SUBSTRING(employee_id FROM 4) AS INTEGER)), 0) + 1
    INTO counter
    FROM profiles
    WHERE employee_id IS NOT NULL AND employee_id ~ '^EMP[0-9]+$';

    -- Formatear como EMP001, EMP002, etc.
    new_id := 'EMP' || LPAD(counter::TEXT, 3, '0');

    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Función para obtener nombre completo
CREATE OR REPLACE FUNCTION get_full_name(profile_record profiles)
RETURNS TEXT AS $$
BEGIN
    RETURN TRIM(
        COALESCE(profile_record.first_name, '') || ' ' ||
        COALESCE(profile_record.second_name, '') || ' ' ||
        COALESCE(profile_record.last_name, '') || ' ' ||
        COALESCE(profile_record.second_last_name, '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Función para obtener el perfil por auth_user_id
CREATE OR REPLACE FUNCTION get_profile_by_auth_id(user_id UUID)
RETURNS profiles AS $$
DECLARE
    profile_record profiles;
BEGIN
    SELECT * INTO profile_record
    FROM profiles
    WHERE auth_user_id = user_id
    LIMIT 1;

    RETURN profile_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para obtener el ID del perfil actual
CREATE OR REPLACE FUNCTION get_current_profile_id()
RETURNS BIGINT AS $$
DECLARE
    profile_id BIGINT;
BEGIN
    SELECT id INTO profile_id
    FROM profiles
    WHERE auth_user_id = auth.uid()
    LIMIT 1;

    RETURN profile_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Vista para obtener información de empleados con nombre completo
CREATE OR REPLACE VIEW employee_view AS
SELECT
    id,
    auth_user_id,
    email,
    get_full_name(profiles.*) as full_name,
    first_name,
    second_name,
    last_name,
    second_last_name,
    phone,
    mobile_phone,
    document_type,
    document_number,
    birth_date,
    nationality,
    address,
    city,
    state,
    postal_code,
    country,
    role,
    job_position,
    department,
    employee_id,
    hire_date,
    salary,
    is_active,
    employment_status,
    avatar_url,
    notes,
    work_schedule,
    created_at,
    updated_at,
    created_by,
    updated_by
FROM profiles
WHERE is_active = true
ORDER BY last_name, first_name;

-- Habilitar Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_positions ENABLE ROW LEVEL SECURITY;

-- Políticas de seguridad para profiles
-- Los usuarios pueden ver su propio perfil
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth_user_id = auth.uid());

-- Los usuarios pueden actualizar su propio perfil (campos limitados)
CREATE POLICY "Users can update their own basic profile" ON profiles
    FOR UPDATE USING (auth_user_id = auth.uid())
    WITH CHECK (auth_user_id = auth.uid());

-- Los administradores pueden ver todos los perfiles
CREATE POLICY "Admins can view all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role = 'admin'
        )
    );

-- Los administradores y supervisores pueden actualizar perfiles de empleados
CREATE POLICY "Admins and supervisors can update employee profiles" ON profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Solo los administradores pueden crear nuevos perfiles manualmente
CREATE POLICY "Admins can insert profiles" ON profiles
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role = 'admin'
        )
    );

-- Solo los administradores pueden eliminar perfiles
CREATE POLICY "Admins can delete profiles" ON profiles
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role = 'admin'
        )
    );

-- Políticas para departments
CREATE POLICY "Everyone can view departments" ON departments
    FOR SELECT USING (true);

CREATE POLICY "Only admins can manage departments" ON departments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Políticas para job_positions
CREATE POLICY "Everyone can view job positions" ON job_positions
    FOR SELECT USING (true);

CREATE POLICY "Only admins can manage job positions" ON job_positions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Insertar datos de ejemplo para departamentos
INSERT INTO departments (name, description) VALUES
('Administración', 'Gestión general del hotel'),
('Recepción', 'Atención al cliente y check-in/check-out'),
('Housekeeping', 'Limpieza y mantenimiento de habitaciones'),
('Seguridad', 'Vigilancia y protección del hotel'),
('Almacén', 'Gestión de inventarios y suministros'),
('Mantenimiento', 'Reparaciones y mantenimiento de instalaciones'),
('Restaurante', 'Servicios de alimentación'),
('Contabilidad', 'Gestión financiera y contable');

-- Insertar posiciones de trabajo
INSERT INTO job_positions (name, description, department_id) VALUES
('gerente', 'Gerente General', (SELECT id FROM departments WHERE name = 'Administración')),
('recepcionista', 'Recepcionista', (SELECT id FROM departments WHERE name = 'Recepción')),
('mucama', 'Mucama/Camarera', (SELECT id FROM departments WHERE name = 'Housekeeping')),
('guardia', 'Guardia de Seguridad', (SELECT id FROM departments WHERE name = 'Seguridad')),
('bodeguero', 'Encargado de Bodega', (SELECT id FROM departments WHERE name = 'Almacén')),
('mantenimiento', 'Técnico de Mantenimiento', (SELECT id FROM departments WHERE name = 'Mantenimiento')),
('chef', 'Chef/Cocinero', (SELECT id FROM departments WHERE name = 'Restaurante')),
('mesero', 'Mesero/Camarero', (SELECT id FROM departments WHERE name = 'Restaurante')),
('contador', 'Contador', (SELECT id FROM departments WHERE name = 'Contabilidad'));

-- Crear tabla de habitaciones
CREATE TABLE IF NOT EXISTS rooms (
    id BIGSERIAL PRIMARY KEY,
    room_number TEXT UNIQUE NOT NULL,
    floor_number INTEGER NOT NULL,
    room_type TEXT NOT NULL CHECK (room_type IN ('individual', 'doble', 'suite', 'familiar', 'presidencial')),
    capacity INTEGER NOT NULL DEFAULT 2 CHECK (capacity > 0),
    price_per_night DECIMAL(10,2) NOT NULL CHECK (price_per_night > 0),

    -- Estado de la habitación
    status TEXT NOT NULL DEFAULT 'libre' CHECK (status IN ('ocupado', 'mantenimiento', 'libre', 'reservado')),

    -- Información adicional
    description TEXT,
    amenities TEXT[], -- WiFi, TV, Aire acondicionado, etc.

    -- Metadatos
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by BIGINT REFERENCES profiles(id),
    updated_by BIGINT REFERENCES profiles(id)
);

-- Crear tabla de historial de estados de habitaciones
CREATE TABLE IF NOT EXISTS room_status_history (
    id BIGSERIAL PRIMARY KEY,
    room_id BIGINT REFERENCES rooms(id) ON DELETE CASCADE NOT NULL,
    previous_status TEXT,
    new_status TEXT NOT NULL CHECK (new_status IN ('ocupado', 'mantenimiento', 'libre', 'reservado')),
    changed_by BIGINT REFERENCES profiles(id) NOT NULL,
    change_reason TEXT,
    notes TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para rooms
CREATE INDEX IF NOT EXISTS idx_rooms_room_number ON rooms(room_number);
CREATE INDEX IF NOT EXISTS idx_rooms_floor_number ON rooms(floor_number);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_is_active ON rooms(is_active);

-- Índices para room_status_history
CREATE INDEX IF NOT EXISTS idx_room_status_history_room_id ON room_status_history(room_id);
CREATE INDEX IF NOT EXISTS idx_room_status_history_changed_at ON room_status_history(changed_at);
CREATE INDEX IF NOT EXISTS idx_room_status_history_new_status ON room_status_history(new_status);

-- Trigger para actualizar updated_at en rooms
CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON rooms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para registrar cambios de estado automáticamente
CREATE OR REPLACE FUNCTION log_room_status_change()
RETURNS TRIGGER AS $$
DECLARE
    current_profile_id BIGINT;
BEGIN
    -- Obtener el ID del perfil actual
    SELECT get_current_profile_id() INTO current_profile_id;

    -- Solo registrar si el estado cambió
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO room_status_history (
            room_id,
            previous_status,
            new_status,
            changed_by,
            change_reason
        ) VALUES (
            NEW.id,
            OLD.status,
            NEW.status,
            COALESCE(current_profile_id, NEW.updated_by),
            'Cambio automático de estado'
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para registrar cambios de estado
CREATE TRIGGER log_room_status_change_trigger
    AFTER UPDATE ON rooms
    FOR EACH ROW
    EXECUTE FUNCTION log_room_status_change();

-- Habilitar Row Level Security para rooms
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_status_history ENABLE ROW LEVEL SECURITY;

-- Políticas para rooms
-- Todos pueden ver las habitaciones (para huéspedes que consulten disponibilidad)
CREATE POLICY "Everyone can view rooms" ON rooms
    FOR SELECT USING (true);

-- Solo admins y supervisores pueden crear habitaciones
CREATE POLICY "Admins and supervisors can insert rooms" ON rooms
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Solo admins y supervisores pueden actualizar habitaciones
CREATE POLICY "Admins and supervisors can update rooms" ON rooms
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Solo admins pueden eliminar habitaciones
CREATE POLICY "Only admins can delete rooms" ON rooms
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role = 'admin'
        )
    );

-- Políticas para room_status_history
-- Solo admins y supervisores pueden ver el historial
CREATE POLICY "Admins and supervisors can view room history" ON room_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Solo admins y supervisores pueden insertar en el historial
CREATE POLICY "Admins and supervisors can insert room history" ON room_status_history
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );

-- Insertar habitaciones de ejemplo
INSERT INTO rooms (room_number, floor_number, room_type, capacity, price_per_night, status, description, amenities, created_by) VALUES
-- Piso 1
('101', 1, 'individual', 1, 80000, 'libre', 'Habitación individual con vista al jardín', ARRAY['wifi', 'tv', 'aire_acondicionado'], 1),
('102', 1, 'doble', 2, 120000, 'ocupado', 'Habitación doble con cama matrimonial', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar'], 1),
('103', 1, 'doble', 2, 120000, 'libre', 'Habitación doble con dos camas sencillas', ARRAY['wifi', 'tv', 'aire_acondicionado'], 1),
('104', 1, 'individual', 1, 80000, 'mantenimiento', 'Habitación individual en mantenimiento', ARRAY['wifi', 'tv'], 1),

-- Piso 2
('201', 2, 'suite', 4, 250000, 'reservado', 'Suite junior con sala de estar', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'jacuzzi'], 1),
('202', 2, 'doble', 2, 140000, 'libre', 'Habitación doble premium', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'balcon'], 1),
('203', 2, 'familiar', 4, 200000, 'ocupado', 'Habitación familiar con litera', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar'], 1),
('204', 2, 'doble', 2, 140000, 'libre', 'Habitación doble con vista a la piscina', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar'], 1),

-- Piso 3
('301', 3, 'suite', 4, 300000, 'libre', 'Suite ejecutiva', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'jacuzzi', 'servicio_habitacion'], 1),
('302', 3, 'presidencial', 6, 500000, 'reservado', 'Suite presidencial con terraza', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'jacuzzi', 'servicio_habitacion', 'terraza'], 1),
('303', 3, 'doble', 2, 160000, 'libre', 'Habitación doble de lujo', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'balcon'], 1),
('304', 3, 'suite', 4, 280000, 'mantenimiento', 'Suite en mantenimiento preventivo', ARRAY['wifi', 'tv', 'aire_acondicionado', 'minibar', 'jacuzzi'], 1);

-- Crear un usuario admin de ejemplo (opcional)
-- Nota: Esto debe hacerse después de que un usuario se registre por primera vez
-- UPDATE profiles SET role = 'admin', job_position = 'gerente', department = 'Administración', employee_id = 'EMP001' WHERE email = '<EMAIL>';

'use client'

import { useState } from 'react'

interface RoomConfigurationManagerProps {
  onConfigChange?: () => void
}

interface RoomTypeConfig {
  value: string
  label: string
  icon: string
  defaultCapacity: number
  defaultPrice: number
  description: string
}

interface AmenityConfig {
  value: string
  label: string
  icon: string
  category: 'basic' | 'comfort' | 'luxury' | 'view'
  description: string
}

const DEFAULT_ROOM_TYPES: RoomTypeConfig[] = [
  {
    value: 'individual',
    label: 'Individual',
    icon: '🛏️',
    defaultCapacity: 1,
    defaultPrice: 80000,
    description: 'Habitación para una persona con cama sencilla'
  },
  {
    value: 'doble',
    label: 'Doble',
    icon: '🛏️🛏️',
    defaultCapacity: 2,
    defaultPrice: 120000,
    description: 'Habitación para dos personas'
  },
  {
    value: 'suite',
    label: 'Suite',
    icon: '🏨',
    defaultCapacity: 3,
    defaultPrice: 200000,
    description: 'Suite con sala de estar separada'
  },
  {
    value: 'familiar',
    label: 'Familiar',
    icon: '👨‍👩‍👧‍👦',
    defaultCapacity: 4,
    defaultPrice: 180000,
    description: 'Habitación amplia para familias'
  },
  {
    value: 'presidencial',
    label: 'Presidencial',
    icon: '👑',
    defaultCapacity: 2,
    defaultPrice: 350000,
    description: 'Suite presidencial de lujo'
  }
]

const DEFAULT_AMENITIES: AmenityConfig[] = [
  // Básicas
  { value: 'wifi', label: 'WiFi', icon: '📶', category: 'basic', description: 'Internet inalámbrico gratuito' },
  { value: 'tv', label: 'TV', icon: '📺', category: 'basic', description: 'Televisión con cable' },
  { value: 'aire_acondicionado', label: 'Aire Acondicionado', icon: '❄️', category: 'basic', description: 'Climatización' },
  
  // Confort
  { value: 'minibar', label: 'Minibar', icon: '🍷', category: 'comfort', description: 'Minibar con bebidas' },
  { value: 'caja_fuerte', label: 'Caja Fuerte', icon: '🔒', category: 'comfort', description: 'Caja fuerte personal' },
  { value: 'servicio_habitacion', label: 'Servicio a la Habitación', icon: '🛎️', category: 'comfort', description: 'Room service 24/7' },
  
  // Lujo
  { value: 'jacuzzi', label: 'Jacuzzi', icon: '🛁', category: 'luxury', description: 'Jacuzzi privado' },
  { value: 'balcon', label: 'Balcón', icon: '🌅', category: 'luxury', description: 'Balcón privado' },
  
  // Vistas
  { value: 'vista_mar', label: 'Vista al Mar', icon: '🌊', category: 'view', description: 'Vista panorámica al océano' },
  { value: 'vista_ciudad', label: 'Vista a la Ciudad', icon: '🏙️', category: 'view', description: 'Vista urbana' },
  { value: 'vista_jardin', label: 'Vista al Jardín', icon: '🌳', category: 'view', description: 'Vista a jardines' }
]

export default function RoomConfigurationManager({ onConfigChange }: RoomConfigurationManagerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'types' | 'amenities'>('types')
  const [roomTypes, setRoomTypes] = useState<RoomTypeConfig[]>(DEFAULT_ROOM_TYPES)
  const [amenities, setAmenities] = useState<AmenityConfig[]>(DEFAULT_AMENITIES)
  const [editingType, setEditingType] = useState<RoomTypeConfig | null>(null)
  const [editingAmenity, setEditingAmenity] = useState<AmenityConfig | null>(null)

  const handleAddRoomType = () => {
    const newType: RoomTypeConfig = {
      value: '',
      label: '',
      icon: '🏠',
      defaultCapacity: 2,
      defaultPrice: 100000,
      description: ''
    }
    setEditingType(newType)
  }

  const handleSaveRoomType = (type: RoomTypeConfig) => {
    if (!type.value || !type.label) {
      alert('Por favor completa todos los campos requeridos')
      return
    }

    if (roomTypes.some(t => t.value === type.value && t !== editingType)) {
      alert('Ya existe un tipo de habitación con ese valor')
      return
    }

    if (editingType && roomTypes.includes(editingType)) {
      setRoomTypes(prev => prev.map(t => t === editingType ? type : t))
    } else {
      setRoomTypes(prev => [...prev, type])
    }
    
    setEditingType(null)
    onConfigChange?.()
  }

  const handleDeleteRoomType = (type: RoomTypeConfig) => {
    if (confirm(`¿Eliminar el tipo "${type.label}"?`)) {
      setRoomTypes(prev => prev.filter(t => t !== type))
      onConfigChange?.()
    }
  }

  const handleAddAmenity = () => {
    const newAmenity: AmenityConfig = {
      value: '',
      label: '',
      icon: '✨',
      category: 'basic',
      description: ''
    }
    setEditingAmenity(newAmenity)
  }

  const handleSaveAmenity = (amenity: AmenityConfig) => {
    if (!amenity.value || !amenity.label) {
      alert('Por favor completa todos los campos requeridos')
      return
    }

    if (amenities.some(a => a.value === amenity.value && a !== editingAmenity)) {
      alert('Ya existe una amenidad con ese valor')
      return
    }

    if (editingAmenity && amenities.includes(editingAmenity)) {
      setAmenities(prev => prev.map(a => a === editingAmenity ? amenity : a))
    } else {
      setAmenities(prev => [...prev, amenity])
    }
    
    setEditingAmenity(null)
    onConfigChange?.()
  }

  const handleDeleteAmenity = (amenity: AmenityConfig) => {
    if (confirm(`¿Eliminar la amenidad "${amenity.label}"?`)) {
      setAmenities(prev => prev.filter(a => a !== amenity))
      onConfigChange?.()
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-blue-100 text-blue-800'
      case 'comfort': return 'bg-green-100 text-green-800'
      case 'luxury': return 'bg-purple-100 text-purple-800'
      case 'view': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="btn-secondary flex items-center space-x-2"
      >
        <span>⚙️</span>
        <span>Configuración</span>
      </button>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-luxury max-w-5xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-luxury font-bold text-dark-900">
              ⚙️ Configuración de Habitaciones
            </h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ✕
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-4 mt-4">
            <button
              onClick={() => setActiveTab('types')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'types'
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              🏠 Tipos de Habitación
            </button>
            <button
              onClick={() => setActiveTab('amenities')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'amenities'
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              ✨ Amenidades
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Tab de Tipos de Habitación */}
          {activeTab === 'types' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Tipos de Habitación ({roomTypes.length})
                </h3>
                <button
                  onClick={handleAddRoomType}
                  className="btn-primary text-sm"
                >
                  ➕ Agregar Tipo
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {roomTypes.map((type, index) => (
                  <div key={index} className="card-luxury p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl">{type.icon}</span>
                        <div>
                          <h4 className="font-semibold text-gray-900">{type.label}</h4>
                          <p className="text-sm text-gray-600">{type.value}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingType(type)}
                          className="text-blue-500 hover:text-blue-700 text-sm"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteRoomType(type)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                    
                    <div className="flex justify-between text-sm">
                      <span>Capacidad: {type.defaultCapacity}</span>
                      <span>Precio: ${type.defaultPrice.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tab de Amenidades */}
          {activeTab === 'amenities' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Amenidades ({amenities.length})
                </h3>
                <button
                  onClick={handleAddAmenity}
                  className="btn-primary text-sm"
                >
                  ➕ Agregar Amenidad
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {amenities.map((amenity, index) => (
                  <div key={index} className="card-luxury p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-xl">{amenity.icon}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{amenity.label}</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(amenity.category)}`}>
                            {amenity.category}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingAmenity(amenity)}
                          className="text-blue-500 hover:text-blue-700 text-sm"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteAmenity(amenity)}
                          className="text-red-500 hover:text-red-700 text-sm"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-600">{amenity.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Modal de edición de tipo de habitación */}
        {editingType && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4">
            <div className="bg-white rounded-xl shadow-luxury max-w-md w-full">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold">
                  {roomTypes.includes(editingType) ? 'Editar' : 'Agregar'} Tipo de Habitación
                </h3>
              </div>
              
              <div className="p-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor (ID)
                  </label>
                  <input
                    type="text"
                    value={editingType.value}
                    onChange={(e) => setEditingType({...editingType, value: e.target.value})}
                    className="input-luxury"
                    placeholder="ej: suite_deluxe"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nombre
                  </label>
                  <input
                    type="text"
                    value={editingType.label}
                    onChange={(e) => setEditingType({...editingType, label: e.target.value})}
                    className="input-luxury"
                    placeholder="ej: Suite Deluxe"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Icono
                    </label>
                    <input
                      type="text"
                      value={editingType.icon}
                      onChange={(e) => setEditingType({...editingType, icon: e.target.value})}
                      className="input-luxury"
                      placeholder="🏠"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Capacidad
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={editingType.defaultCapacity}
                      onChange={(e) => setEditingType({...editingType, defaultCapacity: parseInt(e.target.value)})}
                      className="input-luxury"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Precio por defecto
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="1000"
                    value={editingType.defaultPrice}
                    onChange={(e) => setEditingType({...editingType, defaultPrice: parseFloat(e.target.value)})}
                    className="input-luxury"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descripción
                  </label>
                  <textarea
                    value={editingType.description}
                    onChange={(e) => setEditingType({...editingType, description: e.target.value})}
                    className="input-luxury"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setEditingType(null)}
                  className="btn-secondary"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => handleSaveRoomType(editingType)}
                  className="btn-primary"
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Modal de edición de amenidad */}
        {editingAmenity && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-60 flex items-center justify-center p-4">
            <div className="bg-white rounded-xl shadow-luxury max-w-md w-full">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold">
                  {amenities.includes(editingAmenity) ? 'Editar' : 'Agregar'} Amenidad
                </h3>
              </div>
              
              <div className="p-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valor (ID)
                  </label>
                  <input
                    type="text"
                    value={editingAmenity.value}
                    onChange={(e) => setEditingAmenity({...editingAmenity, value: e.target.value})}
                    className="input-luxury"
                    placeholder="ej: spa_privado"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Nombre
                  </label>
                  <input
                    type="text"
                    value={editingAmenity.label}
                    onChange={(e) => setEditingAmenity({...editingAmenity, label: e.target.value})}
                    className="input-luxury"
                    placeholder="ej: Spa Privado"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Icono
                    </label>
                    <input
                      type="text"
                      value={editingAmenity.icon}
                      onChange={(e) => setEditingAmenity({...editingAmenity, icon: e.target.value})}
                      className="input-luxury"
                      placeholder="✨"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Categoría
                    </label>
                    <select
                      value={editingAmenity.category}
                      onChange={(e) => setEditingAmenity({...editingAmenity, category: e.target.value as any})}
                      className="input-luxury"
                    >
                      <option value="basic">Básica</option>
                      <option value="comfort">Confort</option>
                      <option value="luxury">Lujo</option>
                      <option value="view">Vista</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Descripción
                  </label>
                  <textarea
                    value={editingAmenity.description}
                    onChange={(e) => setEditingAmenity({...editingAmenity, description: e.target.value})}
                    className="input-luxury"
                    rows={2}
                  />
                </div>
              </div>
              
              <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
                <button
                  onClick={() => setEditingAmenity(null)}
                  className="btn-secondary"
                >
                  Cancelar
                </button>
                <button
                  onClick={() => handleSaveAmenity(editingAmenity)}
                  className="btn-primary"
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

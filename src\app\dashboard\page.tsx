'use client'

import { useEffect, useState } from 'react'
import { AdminRoute } from '@/contexts/AuthContext'
import { roomService } from '@/lib/supabase-utils'
import type { Room, RoomStatus } from '@/lib/supabase'
import RoomGrid from '@/components/Dashboard/RoomGrid'
import RoomStatistics from '@/components/Dashboard/RoomStatistics'
import RoomFilters from '@/components/Dashboard/RoomFilters'
import RoomChangeLogs from '@/components/Dashboard/RoomChangeLogs'
import RoomManagement from '@/components/Dashboard/RoomManagement'
import FloorManagement from '@/components/Dashboard/FloorManagement'
import RoomConfigurationManager from '@/components/Dashboard/RoomConfigurationManager'

export default function DashboardPage() {
  const [rooms, setRooms] = useState<Room[]>([])
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedFloor, setSelectedFloor] = useState<number | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<RoomStatus | null>(null)
  const [selectedType, setSelectedType] = useState<string | null>(null)

  useEffect(() => {
    loadRooms()
  }, [])

  useEffect(() => {
    filterRooms()
  }, [rooms, selectedFloor, selectedStatus, selectedType])

  const loadRooms = async () => {
    try {
      setLoading(true)
      const roomsData = await roomService.getAll()
      setRooms(roomsData)
    } catch (error) {
      console.error('Error loading rooms:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterRooms = () => {
    let filtered = [...rooms]

    if (selectedFloor !== null) {
      filtered = filtered.filter(room => room.floor_number === selectedFloor)
    }

    if (selectedStatus !== null) {
      filtered = filtered.filter(room => room.status === selectedStatus)
    }

    if (selectedType !== null) {
      filtered = filtered.filter(room => room.room_type === selectedType)
    }

    setFilteredRooms(filtered)
  }

  const handleRoomsChange = async () => {
    await loadRooms() // Recargar habitaciones
  }

  const clearFilters = () => {
    setSelectedFloor(null)
    setSelectedStatus(null)
    setSelectedType(null)
  }

  if (loading) {
    return (
      <AdminRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Cargando dashboard...</p>
          </div>
        </div>
      </AdminRoute>
    )
  }

  return (
    <AdminRoute>
      <div className="min-h-screen">
        {/* Hero Header */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-luxury"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-luxury font-bold text-white mb-4">
                Dashboard de Habitaciones
              </h1>
              <p className="text-xl text-dark-300 max-w-3xl mx-auto">
                Gestión y monitoreo del estado de las habitaciones del hotel en tiempo real
              </p>

              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/dashboard/users"
                  className="btn-gold inline-flex items-center space-x-2"
                >
                  <span>👥</span>
                  <span>Gestionar Usuarios</span>
                </a>
                <button
                  onClick={() => window.location.reload()}
                  className="btn-luxury-outline inline-flex items-center space-x-2"
                >
                  <span>🔄</span>
                  <span>Actualizar</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Estadísticas */}
          <div className="mb-8">
            <RoomStatistics rooms={rooms} />
          </div>

          {/* Filtros */}
          <div className="mb-6">
            <RoomFilters
              rooms={rooms}
              selectedFloor={selectedFloor}
              selectedStatus={selectedStatus}
              selectedType={selectedType}
              onFloorChange={setSelectedFloor}
              onStatusChange={setSelectedStatus}
              onTypeChange={setSelectedType}
              onClearFilters={clearFilters}
            />
          </div>

          {/* Leyenda de colores moderna */}
          <div className="card-luxury p-6 mb-6">
            <h3 className="text-lg font-semibold text-dark-900 mb-4 flex items-center">
              <span className="mr-2">🎨</span>
              Leyenda de Estados
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center space-x-3 p-3 rounded-xl bg-emerald-50 border border-emerald-200">
                <div className="w-4 h-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-emerald-800">Libre</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-xl bg-red-50 border border-red-200">
                <div className="w-4 h-4 bg-gradient-to-br from-red-500 to-red-600 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-red-800">Ocupado</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-xl bg-blue-50 border border-blue-200">
                <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-blue-800">Reservado</span>
              </div>
              <div className="flex items-center space-x-3 p-3 rounded-xl bg-amber-50 border border-amber-200">
                <div className="w-4 h-4 bg-gradient-to-br from-amber-500 to-amber-600 rounded-full shadow-sm"></div>
                <span className="text-sm font-medium text-amber-800">Mantenimiento</span>
              </div>
            </div>
          </div>

          {/* Grid de habitaciones */}
          <div className="card-luxury p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-luxury font-bold text-dark-900">
                🏨 Habitaciones
                {(selectedFloor || selectedStatus || selectedType) && (
                  <span className="ml-2 text-lg font-normal text-dark-600">
                    ({filteredRooms.length} de {rooms.length})
                  </span>
                )}
              </h2>

              {/* Botones de administración */}
              <div className="flex items-center space-x-3">
                <RoomManagement onRoomsChange={handleRoomsChange} />
                <FloorManagement rooms={rooms} onRoomsChange={handleRoomsChange} />
                <RoomConfigurationManager onConfigChange={handleRoomsChange} />
              </div>
            </div>

            <RoomGrid
              rooms={filteredRooms}
              onRoomsChange={handleRoomsChange}
            />
          </div>

          {/* Sección de logs de actividad */}
          <div className="mt-8">
            <RoomChangeLogs limit={20} />
          </div>
        </div>
      </div>
    </AdminRoute>
  )
}

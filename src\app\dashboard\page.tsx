'use client'

import { useEffect, useState } from 'react'
import { AdminRoute } from '@/contexts/AuthContext'
import { roomService } from '@/lib/supabase-utils'
import type { Room, RoomStatus } from '@/lib/supabase'
import RoomGrid from '@/components/Dashboard/RoomGrid'
import RoomStatistics from '@/components/Dashboard/RoomStatistics'
import RoomFilters from '@/components/Dashboard/RoomFilters'

export default function DashboardPage() {
  const [rooms, setRooms] = useState<Room[]>([])
  const [filteredRooms, setFilteredRooms] = useState<Room[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedFloor, setSelectedFloor] = useState<number | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<RoomStatus | null>(null)
  const [selectedType, setSelectedType] = useState<string | null>(null)

  useEffect(() => {
    loadRooms()
  }, [])

  useEffect(() => {
    filterRooms()
  }, [rooms, selectedFloor, selectedStatus, selectedType])

  const loadRooms = async () => {
    try {
      setLoading(true)
      const roomsData = await roomService.getAll()
      setRooms(roomsData)
    } catch (error) {
      console.error('Error loading rooms:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterRooms = () => {
    let filtered = [...rooms]

    if (selectedFloor !== null) {
      filtered = filtered.filter(room => room.floor_number === selectedFloor)
    }

    if (selectedStatus !== null) {
      filtered = filtered.filter(room => room.status === selectedStatus)
    }

    if (selectedType !== null) {
      filtered = filtered.filter(room => room.room_type === selectedType)
    }

    setFilteredRooms(filtered)
  }

  const handleStatusChange = async (roomId: number, newStatus: RoomStatus, reason?: string) => {
    try {
      await roomService.changeStatus(roomId, newStatus, reason)
      await loadRooms() // Recargar para mostrar cambios
    } catch (error) {
      console.error('Error changing room status:', error)
      alert('Error al cambiar el estado de la habitación')
    }
  }

  const clearFilters = () => {
    setSelectedFloor(null)
    setSelectedStatus(null)
    setSelectedType(null)
  }

  if (loading) {
    return (
      <AdminRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Cargando dashboard...</p>
          </div>
        </div>
      </AdminRoute>
    )
  }

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Dashboard de Habitaciones
                  </h1>
                  <p className="mt-2 text-gray-600">
                    Gestión y monitoreo del estado de las habitaciones del hotel
                  </p>
                </div>

                <div className="flex space-x-3">
                  <a
                    href="/dashboard/users"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    👥 Gestionar Usuarios
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Estadísticas */}
          <div className="mb-8">
            <RoomStatistics rooms={rooms} />
          </div>

          {/* Filtros */}
          <div className="mb-6">
            <RoomFilters
              rooms={rooms}
              selectedFloor={selectedFloor}
              selectedStatus={selectedStatus}
              selectedType={selectedType}
              onFloorChange={setSelectedFloor}
              onStatusChange={setSelectedStatus}
              onTypeChange={setSelectedType}
              onClearFilters={clearFilters}
            />
          </div>

          {/* Grid de habitaciones */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Habitaciones
                  {(selectedFloor || selectedStatus || selectedType) && (
                    <span className="ml-2 text-sm font-normal text-gray-500">
                      ({filteredRooms.length} de {rooms.length})
                    </span>
                  )}
                </h2>

                {/* Leyenda de colores */}
                <div className="flex space-x-4 text-sm">
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
                    <span>Libre</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
                    <span>Ocupado</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                    <span>Reservado</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                    <span>Mantenimiento</span>
                  </div>
                </div>
              </div>

              <RoomGrid
                rooms={filteredRooms}
                onStatusChange={handleStatusChange}
              />
            </div>
          </div>
        </div>
      </div>
    </AdminRoute>
  )
}

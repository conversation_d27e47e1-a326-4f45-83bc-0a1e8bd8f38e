# Dashboard de Gestión de Habitaciones

Sistema completo para monitorear y gestionar el estado de las habitaciones del hotel en tiempo real.

## 🎯 Características Principales

### **Sistema de Estados con Códigos de Color**
- 🟢 **Verde (Libre)**: Habitación disponible para nuevas reservas
- 🔴 **R<PERSON>jo (Ocupado)**: Habitación actualmente ocupada por huéspedes
- 🔵 **Azul (Reservado)**: Habitación reservada para próxima llegada
- 🟠 **Naranja (Mantenimiento)**: Habitación fuera de servicio por mantenimiento

### **Control de Acceso Privilegiado**
- ✅ Solo **Administradores** y **Supervisores** pueden acceder
- ✅ Protección con `AdminRoute` component
- ✅ Verificación de roles en tiempo real

### **Interfaz Visual Intuitiva**
- 📊 **Estadísticas en tiempo real** con métricas clave
- 🏢 **Vista por pisos** organizadamente
- 🔍 **Filtros avanzados** por piso, estado y tipo
- 📱 **Diseño responsive** para móviles y tablets

## 🏗️ Arquitectura del Sistema

### **Base de Datos**

#### Tabla `rooms`
```sql
CREATE TABLE rooms (
    id BIGSERIAL PRIMARY KEY,
    room_number TEXT UNIQUE NOT NULL,
    floor_number INTEGER NOT NULL,
    room_type TEXT NOT NULL, -- individual, doble, suite, familiar, presidencial
    capacity INTEGER NOT NULL,
    price_per_night DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL, -- libre, ocupado, reservado, mantenimiento
    description TEXT,
    amenities TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by BIGINT REFERENCES profiles(id),
    updated_by BIGINT REFERENCES profiles(id)
);
```

#### Tabla `room_status_history`
```sql
CREATE TABLE room_status_history (
    id BIGSERIAL PRIMARY KEY,
    room_id BIGINT REFERENCES rooms(id),
    previous_status TEXT,
    new_status TEXT NOT NULL,
    changed_by BIGINT REFERENCES profiles(id),
    change_reason TEXT,
    notes TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Componentes React**

#### `DashboardPage` - Página principal
- Gestión del estado global de habitaciones
- Coordinación entre componentes
- Manejo de filtros y actualizaciones

#### `RoomGrid` - Grid de habitaciones
- Organización por pisos
- Manejo de clicks en habitaciones
- Modal de cambio de estado

#### `RoomCard` - Tarjeta individual
- Representación visual del estado
- Información básica de la habitación
- Códigos de color intuitivos

#### `RoomStatistics` - Panel de estadísticas
- Métricas en tiempo real
- Cálculo de ocupación
- Ingresos estimados

#### `RoomFilters` - Sistema de filtros
- Filtros por piso, estado y tipo
- Filtros rápidos con botones
- Limpieza de filtros

#### `RoomStatusModal` - Modal de cambio de estado
- Formulario para cambiar estado
- Campo de razón/notas
- Validación y confirmación

## 🔧 Servicios y Funcionalidades

### **`roomService`**
```typescript
// Obtener todas las habitaciones
const rooms = await roomService.getAll()

// Filtrar por estado
const occupiedRooms = await roomService.getByStatus('ocupado')

// Cambiar estado con auditoría
await roomService.changeStatus(roomId, 'mantenimiento', 'Reparación de aire acondicionado')

// Obtener estadísticas
const stats = await roomService.getStatistics()
```

### **`roomHistoryService`**
```typescript
// Historial de una habitación
const history = await roomHistoryService.getRoomHistory(roomId)

// Cambios recientes del sistema
const recentChanges = await roomHistoryService.getRecentHistory(100)

// Cambios por empleado
const employeeChanges = await roomHistoryService.getChangesByEmployee(employeeId)
```

## 📊 Datos de Ejemplo

El sistema incluye **12 habitaciones de ejemplo** distribuidas en 3 pisos:

### **Piso 1** (Habitaciones básicas)
- **101**: Individual - Libre - $80,000/noche
- **102**: Doble - Ocupado - $120,000/noche  
- **103**: Doble - Libre - $120,000/noche
- **104**: Individual - Mantenimiento - $80,000/noche

### **Piso 2** (Habitaciones premium)
- **201**: Suite - Reservado - $250,000/noche
- **202**: Doble Premium - Libre - $140,000/noche
- **203**: Familiar - Ocupado - $200,000/noche
- **204**: Doble Premium - Libre - $140,000/noche

### **Piso 3** (Habitaciones de lujo)
- **301**: Suite Ejecutiva - Libre - $300,000/noche
- **302**: Presidencial - Reservado - $500,000/noche
- **303**: Doble Lujo - Libre - $160,000/noche
- **304**: Suite - Mantenimiento - $280,000/noche

## 🚀 Cómo Usar el Dashboard

### **1. Acceso al Sistema**
```bash
# Navegar al dashboard
http://localhost:3000/dashboard

# Requiere login con rol admin o supervisor
```

### **2. Navegación Principal**
- **Estadísticas**: Panel superior con métricas clave
- **Filtros**: Herramientas de búsqueda y filtrado
- **Grid de habitaciones**: Vista principal organizada por pisos
- **Leyenda de colores**: Referencia visual de estados

### **3. Cambiar Estado de Habitación**
1. Click en cualquier habitación
2. Se abre modal con información detallada
3. Seleccionar nuevo estado
4. Agregar razón del cambio (opcional)
5. Confirmar cambios

### **4. Filtros Disponibles**
- **Por piso**: 1, 2, 3
- **Por estado**: Libre, Ocupado, Reservado, Mantenimiento
- **Por tipo**: Individual, Doble, Suite, Familiar, Presidencial
- **Filtros rápidos**: Botones de acceso directo

## 🔐 Seguridad y Permisos

### **Row Level Security (RLS)**
```sql
-- Solo admins y supervisores pueden modificar habitaciones
CREATE POLICY "Admins and supervisors can update rooms" ON rooms
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE auth_user_id = auth.uid() AND role IN ('admin', 'supervisor')
        )
    );
```

### **Auditoría Automática**
- Todos los cambios de estado se registran automáticamente
- Trigger que captura: quién, cuándo, qué cambió
- Historial completo para análisis y reportes

### **Validaciones**
- Estados válidos únicamente
- Campos requeridos verificados
- Permisos verificados en cada operación

## 📈 Métricas y Estadísticas

### **Panel de Estadísticas**
- **Total de habitaciones**: Contador general
- **Habitaciones libres**: Disponibles + porcentaje
- **Habitaciones ocupadas**: En uso + tasa de ocupación
- **Ingresos diarios**: Cálculo automático basado en ocupadas + reservadas

### **Distribución por Estado**
- Contadores visuales por cada estado
- Iconos representativos para cada categoría
- Actualización en tiempo real

## 🔄 Flujo de Trabajo Típico

### **Llegada de Huésped**
1. Habitación en estado "Reservado" (azul)
2. Al hacer check-in → cambiar a "Ocupado" (rojo)
3. Razón: "Check-in completado"

### **Salida de Huésped**
1. Habitación en estado "Ocupado" (rojo)
2. Al hacer check-out → cambiar a "Libre" (verde)
3. Razón: "Check-out completado"

### **Mantenimiento Programado**
1. Habitación en estado "Libre" (verde)
2. Para mantenimiento → cambiar a "Mantenimiento" (naranja)
3. Razón: "Mantenimiento preventivo de aire acondicionado"

### **Nueva Reserva**
1. Habitación en estado "Libre" (verde)
2. Al confirmar reserva → cambiar a "Reservado" (azul)
3. Razón: "Nueva reserva confirmada"

## 🎨 Personalización Visual

### **Códigos de Color**
```css
.libre { background-color: #10b981; }      /* Verde */
.ocupado { background-color: #ef4444; }    /* Rojo */
.reservado { background-color: #3b82f6; }  /* Azul */
.mantenimiento { background-color: #f59e0b; } /* Naranja */
```

### **Iconos Representativos**
- ✓ Libre
- 👤 Ocupado  
- 📅 Reservado
- 🔧 Mantenimiento

## 🚀 Próximas Mejoras

- [ ] **Drag & Drop**: Cambiar estados arrastrando
- [ ] **Vista de calendario**: Reservas por fechas
- [ ] **Notificaciones**: Alertas de cambios importantes
- [ ] **Reportes**: Exportar estadísticas
- [ ] **Integración**: Conectar con sistema de reservas
- [ ] **Tiempo real**: WebSockets para actualizaciones automáticas

¡El dashboard está completamente funcional y listo para gestionar las habitaciones de tu hotel de manera eficiente y profesional!

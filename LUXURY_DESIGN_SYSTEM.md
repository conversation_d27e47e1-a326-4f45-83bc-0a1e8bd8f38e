# Sistema de Diseño Luxury - Hotel Management

Diseño moderno de 5 estrellas con paleta de colores premium (más rojo que negro, más negro que blanco).

## 🎨 Paleta de Colores

### **Colores Primarios (Rojos Premium)**
```css
primary: {
  50: '#fef2f2',   /* Rojo muy claro */
  100: '#fee2e2',  /* Rojo claro */
  200: '#fecaca',  /* Rojo suave */
  300: '#fca5a5',  /* Rojo medio claro */
  400: '#f87171',  /* Rojo medio */
  500: '#ef4444',  /* Rojo base */
  600: '#dc2626',  /* Rojo intenso */
  700: '#b91c1c',  /* Rojo oscuro */
  800: '#991b1b',  /* Rojo muy oscuro */
  900: '#7f1d1d',  /* Rojo profundo */
  950: '#450a0a',  /* Rojo casi negro */
}
```

### **Colores Oscuros (Grises Premium)**
```css
dark: {
  50: '#f8fafc',   /* Blanco grisáceo */
  100: '#f1f5f9',  /* Gris muy claro */
  200: '#e2e8f0',  /* Gris claro */
  300: '#cbd5e1',  /* Gris medio claro */
  400: '#94a3b8',  /* Gris medio */
  500: '#64748b',  /* Gris base */
  600: '#475569',  /* Gris oscuro */
  700: '#334155',  /* Gris muy oscuro */
  800: '#1e293b',  /* Gris profundo */
  900: '#0f172a',  /* Gris casi negro */
  950: '#020617',  /* Negro grisáceo */
}
```

### **Colores de Lujo**
```css
luxury: {
  gold: '#d4af37',        /* Oro clásico */
  'gold-light': '#f4e4a6', /* Oro claro */
  'gold-dark': '#b8941f',  /* Oro oscuro */
  platinum: '#e5e4e2',     /* Platino */
  'platinum-dark': '#c0bfbd', /* Platino oscuro */
  bronze: '#cd7f32',       /* Bronce */
}
```

### **Estados de Habitaciones**
```css
status: {
  libre: '#10b981',        /* Verde esmeralda */
  ocupado: '#dc2626',      /* Rojo intenso */
  reservado: '#3b82f6',    /* Azul real */
  mantenimiento: '#f59e0b', /* Ámbar dorado */
}
```

## 🎭 Tipografía

### **Fuentes Premium**
- **Luxury**: `Playfair Display` - Para títulos y elementos elegantes
- **Modern**: `Inter` - Para texto general y UI

### **Clases de Texto**
```css
.text-luxury {
  font-family: 'Playfair Display';
  background: linear-gradient(135deg, #d4af37 0%, #f4e4a6 50%, #b8941f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

## 🎨 Gradientes

### **Gradientes de Fondo**
```css
.bg-gradient-luxury {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 50%, #dc2626 100%);
}

.bg-gradient-gold {
  background: linear-gradient(135deg, #d4af37 0%, #f4e4a6 50%, #b8941f 100%);
}

.bg-gradient-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.bg-gradient-dark {
  background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
}
```

## 🔘 Componentes de Botones

### **Botón Luxury (Primario)**
```css
.btn-luxury {
  @apply px-6 py-3 bg-gradient-primary text-white font-medium rounded-xl 
         shadow-luxury hover:shadow-glow transition-all duration-300 
         transform hover:scale-105 focus:outline-none focus:ring-4 
         focus:ring-primary-500/30;
}
```

### **Botón Luxury Outline**
```css
.btn-luxury-outline {
  @apply px-6 py-3 border-2 border-primary-600 text-primary-600 
         font-medium rounded-xl hover:bg-primary-600 hover:text-white 
         transition-all duration-300 transform hover:scale-105 
         focus:outline-none focus:ring-4 focus:ring-primary-500/30;
}
```

### **Botón Gold (Premium)**
```css
.btn-gold {
  @apply px-6 py-3 bg-gradient-gold text-dark-900 font-medium 
         rounded-xl shadow-luxury hover:shadow-glow-gold 
         transition-all duration-300 transform hover:scale-105 
         focus:outline-none focus:ring-4 focus:ring-luxury-gold/30;
}
```

## 🃏 Componentes de Tarjetas

### **Tarjeta Luxury (Clara)**
```css
.card-luxury {
  @apply bg-gradient-card backdrop-blur-luxury border border-dark-200/20 
         rounded-2xl shadow-luxury hover:shadow-card-hover 
         transition-all duration-300 transform hover:scale-[1.02];
}
```

### **Tarjeta Dark (Oscura)**
```css
.card-dark {
  @apply bg-gradient-dark backdrop-blur-luxury border border-dark-600/30 
         rounded-2xl shadow-luxury text-dark-100;
}
```

## 📝 Inputs y Formularios

### **Input Luxury**
```css
.input-luxury {
  @apply w-full px-4 py-3 bg-white/90 backdrop-blur-sm border 
         border-dark-200 rounded-xl focus:outline-none focus:ring-4 
         focus:ring-primary-500/30 focus:border-primary-500 
         transition-all duration-300 placeholder-dark-400;
}
```

## 🏨 Estados de Habitaciones

### **Habitación Libre**
```css
.room-libre {
  @apply bg-gradient-to-br from-emerald-500 to-emerald-600 
         text-white shadow-lg hover:shadow-xl;
}
```

### **Habitación Ocupada**
```css
.room-ocupado {
  @apply bg-gradient-to-br from-red-500 to-red-600 
         text-white shadow-lg hover:shadow-xl;
}
```

### **Habitación Reservada**
```css
.room-reservado {
  @apply bg-gradient-to-br from-blue-500 to-blue-600 
         text-white shadow-lg hover:shadow-xl;
}
```

### **Habitación en Mantenimiento**
```css
.room-mantenimiento {
  @apply bg-gradient-to-br from-amber-500 to-amber-600 
         text-white shadow-lg hover:shadow-xl;
}
```

## ✨ Efectos y Animaciones

### **Sombras Premium**
```css
shadow-luxury: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
shadow-glow: '0 0 20px rgba(220, 38, 38, 0.3)',
shadow-glow-gold: '0 0 20px rgba(212, 175, 55, 0.4)',
```

### **Animaciones**
```css
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.hover-lift {
  @apply transition-all duration-300 hover:transform 
         hover:scale-105 hover:shadow-luxury;
}
```

### **Keyframes**
```css
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 🌟 Efectos de Cristal

### **Glass Effect**
```css
.glass-effect {
  @apply bg-white/10 backdrop-blur-luxury border border-white/20 rounded-2xl;
}
```

### **Backdrop Blur**
```css
backdrop-blur-luxury: '20px',
```

## 🎯 Uso en Componentes

### **Navbar**
- Fondo: `bg-dark-900/95 backdrop-blur-luxury`
- Bordes: `border-dark-700/50`
- Logo: `bg-gradient-primary` con efecto `shadow-glow`

### **Dashboard**
- Fondo principal: `bg-gradient-luxury`
- Tarjetas: `card-luxury` y `card-dark`
- Estadísticas: Gradientes específicos por estado

### **Login**
- Fondo: `bg-gradient-luxury` con efectos de partículas
- Formulario: `card-luxury` con `backdrop-blur-luxury`
- Botones: `btn-luxury` y `btn-gold`

### **Habitaciones**
- Estados visuales con gradientes específicos
- Efectos hover con `hover:shadow-xl`
- Bordes redondeados `rounded-2xl`

## 🚀 Implementación

### **Instalación**
```bash
npm install -D tailwindcss@^3.4.0 postcss autoprefixer
npx tailwindcss init -p
```

### **Configuración**
1. Actualizar `tailwind.config.js` con la paleta personalizada
2. Importar fuentes de Google Fonts
3. Configurar `@tailwind` directives en `globals.css`
4. Agregar componentes personalizados en `@layer components`

### **Resultado**
- ✅ Diseño moderno y elegante de 5 estrellas
- ✅ Paleta de colores premium (rojo > negro > blanco)
- ✅ Efectos visuales sofisticados
- ✅ Animaciones suaves y profesionales
- ✅ Componentes reutilizables
- ✅ Responsive design completo

¡El sistema de diseño está completamente implementado y funcionando! 🎉

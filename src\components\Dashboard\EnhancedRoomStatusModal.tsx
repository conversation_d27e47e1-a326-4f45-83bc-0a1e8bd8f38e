'use client'

import { useState, useEffect } from 'react'
import { roomOccupancyService } from '@/lib/supabase-utils'
import type { Room, RoomOccupancy } from '@/lib/supabase'

interface EnhancedRoomStatusModalProps {
  room: Room | null
  isOpen: boolean
  onClose: () => void
  onStatusChanged: () => void
}

type ActionType = 'reserve' | 'checkin' | 'checkout' | 'maintenance' | 'clean'

export default function EnhancedRoomStatusModal({
  room,
  isOpen,
  onClose,
  onStatusChanged
}: EnhancedRoomStatusModalProps) {
  const [action, setAction] = useState<ActionType>('reserve')
  const [loading, setLoading] = useState(false)
  const [occupancy, setOccupancy] = useState<RoomOccupancy | null>(null)
  
  // Datos del formulario
  const [guestName, setGuestName] = useState('')
  const [guestPhone, setGuestPhone] = useState('')
  const [guestDocument, setGuestDocument] = useState('')
  const [guestCount, setGuestCount] = useState(1)
  const [reservationDate, setReservationDate] = useState('')
  const [reservationTime, setReservationTime] = useState('')
  const [checkInDate, setCheckInDate] = useState('')
  const [checkInTime, setCheckInTime] = useState('')
  const [checkOutDate, setCheckOutDate] = useState('')
  const [checkOutTime, setCheckOutTime] = useState('')
  const [notes, setNotes] = useState('')
  const [specialRequests, setSpecialRequests] = useState('')

  useEffect(() => {
    if (room && isOpen) {
      loadOccupancy()
      setDefaultDates()
    }
  }, [room, isOpen])

  const loadOccupancy = async () => {
    if (!room) return
    
    try {
      const data = await roomOccupancyService.getRoomOccupancy(room.id)
      setOccupancy(data)
      
      if (data) {
        setGuestName(data.guest_name || '')
        setGuestPhone(data.guest_phone || '')
        setGuestDocument(data.guest_document || '')
        setGuestCount(data.guest_count || 1)
        setReservationDate(data.reservation_date || '')
        setReservationTime(data.reservation_time || '')
        setCheckInDate(data.check_in_date || '')
        setCheckInTime(data.check_in_time || '')
        setCheckOutDate(data.check_out_date || '')
        setCheckOutTime(data.check_out_time || '')
        setNotes(data.notes || '')
        setSpecialRequests(data.special_requests || '')
      }
    } catch (error) {
      console.error('Error loading occupancy:', error)
    }
  }

  const setDefaultDates = () => {
    const today = new Date().toISOString().split('T')[0]
    const now = new Date().toTimeString().slice(0, 5)
    
    if (!checkInDate) setCheckInDate(today)
    if (!checkInTime) setCheckInTime(now)
    if (!reservationDate) setReservationDate(today)
    if (!reservationTime) setReservationTime(now)
    
    // Check-out por defecto al día siguiente a las 12:00
    if (!checkOutDate) {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      setCheckOutDate(tomorrow.toISOString().split('T')[0])
    }
    if (!checkOutTime) setCheckOutTime('12:00')
  }

  const getAvailableActions = (): ActionType[] => {
    if (!room) return []
    
    switch (room.status) {
      case 'libre':
        return ['reserve', 'checkin', 'maintenance']
      case 'reservado':
        return ['checkin', 'maintenance']
      case 'ocupado':
        return ['checkout', 'maintenance']
      case 'mantenimiento':
        return ['clean']
      default:
        return []
    }
  }

  const getActionLabel = (actionType: ActionType): string => {
    switch (actionType) {
      case 'reserve': return 'Crear Reserva'
      case 'checkin': return 'Realizar Check-in'
      case 'checkout': return 'Realizar Check-out'
      case 'maintenance': return 'Enviar a Mantenimiento'
      case 'clean': return 'Marcar como Limpia'
      default: return actionType
    }
  }

  const getActionColor = (actionType: ActionType): string => {
    switch (actionType) {
      case 'reserve': return 'bg-blue-600 hover:bg-blue-700'
      case 'checkin': return 'bg-green-600 hover:bg-green-700'
      case 'checkout': return 'bg-orange-600 hover:bg-orange-700'
      case 'maintenance': return 'bg-yellow-600 hover:bg-yellow-700'
      case 'clean': return 'bg-emerald-600 hover:bg-emerald-700'
      default: return 'bg-gray-600 hover:bg-gray-700'
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!room) return

    setLoading(true)
    try {
      switch (action) {
        case 'reserve':
          await roomOccupancyService.createReservation(room.id, {
            guestName,
            guestPhone,
            guestDocument,
            guestCount,
            reservationDate,
            reservationTime,
            checkInDate,
            checkInTime,
            checkOutDate,
            checkOutTime,
            notes,
            specialRequests
          })
          break
          
        case 'checkin':
          await roomOccupancyService.checkIn(room.id, {
            guestName: guestName || occupancy?.guest_name,
            guestPhone: guestPhone || occupancy?.guest_phone,
            guestDocument: guestDocument || occupancy?.guest_document,
            guestCount: guestCount || occupancy?.guest_count || 1,
            checkInDate,
            checkInTime,
            checkOutDate,
            checkOutTime,
            notes
          })
          break
          
        case 'checkout':
          await roomOccupancyService.checkOut(room.id, notes)
          break
          
        case 'maintenance':
          await roomOccupancyService.sendToMaintenance(room.id, notes, notes)
          break
          
        case 'clean':
          await roomOccupancyService.markAsClean(room.id, notes)
          break
      }
      
      onStatusChanged()
      onClose()
      resetForm()
    } catch (error: any) {
      console.error('Error:', error)
      alert(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setGuestName('')
    setGuestPhone('')
    setGuestDocument('')
    setGuestCount(1)
    setNotes('')
    setSpecialRequests('')
    setDefaultDates()
  }

  if (!isOpen || !room) return null

  const availableActions = getAvailableActions()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Gestionar Habitación {room.room_number}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {/* Información actual */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Estado Actual</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Estado:</span>
                <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                  room.status === 'libre' ? 'bg-green-100 text-green-800' :
                  room.status === 'ocupado' ? 'bg-red-100 text-red-800' :
                  room.status === 'reservado' ? 'bg-blue-100 text-blue-800' :
                  'bg-orange-100 text-orange-800'
                }`}>
                  {room.status}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Tipo:</span>
                <span className="ml-2">{room.room_type}</span>
              </div>
              <div>
                <span className="text-gray-600">Capacidad:</span>
                <span className="ml-2">{room.capacity} personas</span>
              </div>
              <div>
                <span className="text-gray-600">Piso:</span>
                <span className="ml-2">{room.floor_number}</span>
              </div>
            </div>
            
            {occupancy?.guest_name && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <span className="text-gray-600">Huésped actual:</span>
                <span className="ml-2 font-medium">{occupancy.guest_name}</span>
                {occupancy.guest_count && occupancy.guest_count > 1 && (
                  <span className="ml-2 text-gray-500">({occupancy.guest_count} personas)</span>
                )}
              </div>
            )}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Selección de acción */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Acción a realizar
              </label>
              <div className="grid grid-cols-1 gap-2">
                {availableActions.map((actionType) => (
                  <button
                    key={actionType}
                    type="button"
                    onClick={() => setAction(actionType)}
                    className={`p-3 text-left rounded-lg border-2 transition-colors ${
                      action === actionType
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">{getActionLabel(actionType)}</div>
                    <div className="text-sm text-gray-500">
                      {actionType === 'reserve' && 'Crear una nueva reserva con fecha y hora'}
                      {actionType === 'checkin' && 'Registrar entrada del huésped'}
                      {actionType === 'checkout' && 'Registrar salida del huésped'}
                      {actionType === 'maintenance' && 'Enviar habitación a mantenimiento'}
                      {actionType === 'clean' && 'Marcar habitación como limpia y disponible'}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Campos específicos según la acción */}
            {(action === 'reserve' || action === 'checkin') && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Información del Huésped</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nombre del huésped *
                    </label>
                    <input
                      type="text"
                      value={guestName}
                      onChange={(e) => setGuestName(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Número de personas
                    </label>
                    <input
                      type="number"
                      min="1"
                      max={room.capacity}
                      value={guestCount}
                      onChange={(e) => setGuestCount(parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Teléfono
                    </label>
                    <input
                      type="tel"
                      value={guestPhone}
                      onChange={(e) => setGuestPhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Documento
                    </label>
                    <input
                      type="text"
                      value={guestDocument}
                      onChange={(e) => setGuestDocument(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {action === 'reserve' && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Fechas de Reserva</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fecha de reserva *
                    </label>
                    <input
                      type="date"
                      value={reservationDate}
                      onChange={(e) => setReservationDate(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hora de reserva *
                    </label>
                    <input
                      type="time"
                      value={reservationTime}
                      onChange={(e) => setReservationTime(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {(action === 'reserve' || action === 'checkin') && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Fechas de Estadía</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fecha de entrada *
                    </label>
                    <input
                      type="date"
                      value={checkInDate}
                      onChange={(e) => setCheckInDate(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hora de entrada *
                    </label>
                    <input
                      type="time"
                      value={checkInTime}
                      onChange={(e) => setCheckInTime(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fecha de salida *
                    </label>
                    <input
                      type="date"
                      value={checkOutDate}
                      onChange={(e) => setCheckOutDate(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hora de salida *
                    </label>
                    <input
                      type="time"
                      value={checkOutTime}
                      onChange={(e) => setCheckOutTime(e.target.value)}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {action === 'reserve' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Solicitudes especiales
                </label>
                <textarea
                  value={specialRequests}
                  onChange={(e) => setSpecialRequests(e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Cama extra, vista al mar, etc."
                />
              </div>
            )}

            {/* Notas */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notas adicionales
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Observaciones, razón del cambio, etc."
              />
            </div>

            {/* Botones */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancelar
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-2 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${getActionColor(action)}`}
              >
                {loading ? 'Procesando...' : getActionLabel(action)}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

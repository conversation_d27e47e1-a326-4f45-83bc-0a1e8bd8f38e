# Sistema Avanzado de Gestión de Habitaciones

Sistema completo con fechas/horas, logs detallados y automatización para la gestión profesional de habitaciones de hotel.

## 🎯 Nuevas Características Implementadas

### ✅ **1. Eliminación de Ingresos del Dashboard**
- Removido el cálculo y visualización de ingresos diarios
- Dashboard enfocado en gestión operativa
- Estadísticas simplificadas: Total, Libres, Ocupadas, Mantenimiento

### ✅ **2. Sistema de Logs Detallados**
- **Tabla `room_change_logs`**: Registro completo de todos los cambios
- **Información capturada**:
  - Usuario que realizó el cambio (nombre y rol)
  - Fecha y hora exacta del cambio
  - Estado anterior y nuevo estado
  - Tipo de cambio (manual, automático, programado)
  - Razón del cambio y notas adicionales
  - Datos adicionales en formato JSON

### ✅ **3. Gestión de Fechas y Horas**
- **Tabla `room_occupancy`**: Información completa de ocupación
- **Campos implementados**:
  - Fecha y hora de reserva
  - Fecha y hora de check-in
  - Fecha y hora de check-out
  - Información del huésped (nombre, teléfono, documento, cantidad)
  - Notas y solicitudes especiales

### ✅ **4. Modal Mejorado para Gestión**
- **Acciones disponibles según estado actual**:
  - **Libre** → Crear Reserva, Check-in Directo, Mantenimiento
  - **Reservado** → Check-in, Mantenimiento
  - **Ocupado** → Check-out, Mantenimiento
  - **Mantenimiento** → Marcar como Limpia (mucama)

### ✅ **5. Automatización de Estados**
- **Reserva → Ocupado**: Cuando llega la hora de la reserva
- **Ocupado → Mantenimiento**: Cuando se cumple la hora de check-out
- **Función `process_automatic_status_changes()`** para ejecutar cambios programados

## 🏗️ Arquitectura de Base de Datos

### **Tabla `room_occupancy`**
```sql
CREATE TABLE room_occupancy (
    id BIGSERIAL PRIMARY KEY,
    room_id BIGINT REFERENCES rooms(id) UNIQUE,
    
    -- Fechas de reserva
    reservation_date DATE,
    reservation_time TIME,
    
    -- Fechas de estadía
    check_in_date DATE,
    check_in_time TIME,
    check_out_date DATE,
    check_out_time TIME,
    
    -- Estado actual
    current_status TEXT CHECK (current_status IN ('ocupado', 'mantenimiento', 'libre', 'reservado')),
    
    -- Información del huésped
    guest_name TEXT,
    guest_phone TEXT,
    guest_document TEXT,
    guest_count INTEGER DEFAULT 1,
    
    -- Notas
    notes TEXT,
    special_requests TEXT,
    
    -- Metadatos
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by BIGINT REFERENCES profiles(id),
    updated_by BIGINT REFERENCES profiles(id)
);
```

### **Tabla `room_change_logs`**
```sql
CREATE TABLE room_change_logs (
    id BIGSERIAL PRIMARY KEY,
    room_id BIGINT REFERENCES rooms(id),
    occupancy_id BIGINT REFERENCES room_occupancy(id),
    
    -- Información del cambio
    previous_status TEXT,
    new_status TEXT NOT NULL,
    change_type TEXT CHECK (change_type IN ('manual', 'automatic', 'scheduled')),
    
    -- Fechas
    scheduled_for TIMESTAMP WITH TIME ZONE,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Usuario
    changed_by BIGINT REFERENCES profiles(id),
    user_role TEXT,
    user_name TEXT,
    
    -- Detalles
    reason TEXT,
    notes TEXT,
    additional_data JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔧 Funciones de Base de Datos

### **`manage_room_occupancy()`**
Función principal para gestionar ocupación con diferentes acciones:

```sql
-- Crear reserva
SELECT manage_room_occupancy(
    p_room_id := 101,
    p_action := 'reserve',
    p_guest_name := 'Juan Pérez',
    p_reservation_date := '2025-01-25',
    p_reservation_time := '15:00'
);

-- Realizar check-in
SELECT manage_room_occupancy(
    p_room_id := 101,
    p_action := 'checkin',
    p_check_in_date := '2025-01-25',
    p_check_in_time := '15:30',
    p_check_out_date := '2025-01-27',
    p_check_out_time := '12:00'
);
```

### **`change_room_status_detailed()`**
Función para cambios de estado con logging automático:

```sql
SELECT change_room_status_detailed(
    p_room_id := 101,
    p_new_status := 'mantenimiento',
    p_reason := 'Reparación programada',
    p_notes := 'Cambio de aire acondicionado'
);
```

### **`process_automatic_status_changes()`**
Función para automatización (ejecutar periódicamente):

```sql
-- Procesar cambios automáticos
SELECT process_automatic_status_changes();

-- Ver habitaciones pendientes de cambio
SELECT * FROM get_rooms_pending_auto_change();
```

## 🎨 Componentes React

### **`EnhancedRoomStatusModal`**
Modal inteligente que adapta las opciones según el estado actual:

- **Formularios dinámicos** según la acción seleccionada
- **Validaciones automáticas** de fechas y horas
- **Información contextual** del estado actual
- **Campos específicos** para cada tipo de operación

### **`RoomChangeLogs`**
Componente para visualizar el historial de cambios:

- **Vista cronológica** de todos los cambios
- **Filtros por habitación** o vista general
- **Iconos diferenciados** por tipo de cambio
- **Información detallada** de cada operación

## 🚀 Flujos de Trabajo

### **1. Crear Reserva**
```typescript
// Usuario selecciona habitación libre
// Modal muestra formulario de reserva
await roomOccupancyService.createReservation(roomId, {
  guestName: "Juan Pérez",
  guestPhone: "+57 ************",
  guestCount: 2,
  reservationDate: "2025-01-25",
  reservationTime: "15:00",
  checkInDate: "2025-01-25",
  checkInTime: "15:00",
  checkOutDate: "2025-01-27",
  checkOutTime: "12:00",
  specialRequests: "Vista al mar"
})
```

### **2. Realizar Check-in**
```typescript
// Usuario selecciona habitación reservada
// Modal muestra datos de la reserva existente
await roomOccupancyService.checkIn(roomId, {
  checkInDate: "2025-01-25",
  checkInTime: "15:30",
  checkOutDate: "2025-01-27",
  checkOutTime: "12:00",
  notes: "Huésped llegó temprano"
})
```

### **3. Realizar Check-out**
```typescript
// Usuario selecciona habitación ocupada
// Modal simple para confirmar check-out
await roomOccupancyService.checkOut(roomId, "Check-out normal")
// Estado cambia automáticamente a 'mantenimiento'
```

### **4. Mucama Marca como Limpia**
```typescript
// Mucama selecciona habitación en mantenimiento
// Modal simple para confirmar limpieza
await roomOccupancyService.markAsClean(roomId, "Habitación lista")
// Estado cambia a 'libre'
```

## ⏰ Automatización

### **Cambios Automáticos Programados**

1. **Reserva → Ocupado**
   - Se ejecuta cuando llega la hora de la reserva
   - Ventana de 30 minutos de tolerancia
   - Log automático con detalles de la reserva

2. **Ocupado → Mantenimiento**
   - Se ejecuta cuando se cumple la hora de check-out
   - Cambio inmediato sin tolerancia
   - Log automático con información del check-out

### **Implementación de Automatización**
```typescript
// Ejecutar cada 15 minutos (cron job o similar)
const result = await roomOccupancyService.processAutomaticChanges()
console.log(`Cambios automáticos realizados: ${result.changes_made}`)

// Ver habitaciones pendientes
const pending = await roomOccupancyService.getRoomsPendingAutoChange()
```

## 📊 Logs y Auditoría

### **Información Capturada**
- ✅ **Usuario completo**: Nombre, rol, ID
- ✅ **Timestamps precisos**: Fecha y hora exacta
- ✅ **Estados**: Anterior y nuevo
- ✅ **Tipo de cambio**: Manual, automático, programado
- ✅ **Contexto**: Razón, notas, datos adicionales
- ✅ **Trazabilidad**: Relación con ocupación

### **Consultas de Auditoría**
```typescript
// Logs de una habitación específica
const roomLogs = await roomChangeLogsService.getRoomLogs(roomId, 50)

// Actividad reciente del sistema
const recentLogs = await roomChangeLogsService.getRecentLogs(100)

// Cambios realizados por un usuario
const userLogs = await roomChangeLogsService.getUserLogs(userId, 50)

// Estadísticas de cambios
const stats = await roomChangeLogsService.getChangeStatistics(7) // últimos 7 días
```

## 🔐 Seguridad y Permisos

### **Row Level Security (RLS)**
- ✅ Usuarios autenticados pueden **ver** ocupaciones y logs
- ✅ Solo admins/supervisores pueden **modificar** datos
- ✅ Logs son **solo lectura** para preservar auditoría

### **Validaciones**
- ✅ Estados válidos únicamente
- ✅ Fechas coherentes (check-out después de check-in)
- ✅ Capacidad de habitación respetada
- ✅ Permisos verificados en cada operación

## 🎯 Beneficios del Nuevo Sistema

### **Para Administradores**
- ✅ **Trazabilidad completa** de todos los cambios
- ✅ **Automatización** reduce errores manuales
- ✅ **Informes detallados** para análisis
- ✅ **Control granular** de operaciones

### **Para Recepcionistas**
- ✅ **Proceso guiado** para cada operación
- ✅ **Información contextual** siempre visible
- ✅ **Validaciones automáticas** previenen errores
- ✅ **Historial accesible** para consultas

### **Para Mucamas**
- ✅ **Proceso simple** para marcar habitaciones limpias
- ✅ **Estado claro** de qué habitaciones necesitan limpieza
- ✅ **Registro automático** de su trabajo

### **Para el Sistema**
- ✅ **Datos estructurados** para reportes
- ✅ **Automatización** reduce carga operativa
- ✅ **Escalabilidad** para hoteles grandes
- ✅ **Integración** fácil con otros sistemas

¡El sistema está completamente implementado y listo para gestionar habitaciones de manera profesional y eficiente! 🎉

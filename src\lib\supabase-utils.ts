import { supabase, type Profile, type Department, type JobPosition, type EmployeeView, type UserRole, type JobPositionType } from './supabase'

// Funciones para manejar empleados/perfiles
export const employeeService = {
  // Obtener todos los empleados (solo admins y supervisores)
  async getAll() {
    const { data, error } = await supabase
      .from('employee_view')
      .select('*')
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as EmployeeView[]
  },

  // Obtener empleados activos
  async getActive() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Obtener empleado por ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Profile
  },

  // Obtener empleados por departamento
  async getByDepartment(department: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('department', department)
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Obtener empleados por posición
  async getByJobPosition(jobPosition: JobPositionType) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('job_position', jobPosition)
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Crear nuevo empleado (solo admins)
  async create(employee: Omit<Profile, 'id' | 'created_at' | 'updated_at' | 'employee_id'>) {
    // Generar ID de empleado automáticamente
    const { data: employeeId, error: idError } = await supabase
      .rpc('generate_employee_id')

    if (idError) throw idError

    const { data, error } = await supabase
      .from('profiles')
      .insert({
        ...employee,
        employee_id: employeeId,
        hire_date: employee.hire_date || new Date().toISOString().split('T')[0]
      })
      .select()
      .single()

    if (error) throw error
    return data as Profile
  },

  // Actualizar empleado
  async update(id: string, updates: Partial<Profile>) {
    const { data: { user } } = await supabase.auth.getUser()

    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_by: user?.id
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Profile
  },

  // Desactivar empleado (soft delete)
  async deactivate(id: string) {
    return this.update(id, {
      is_active: false,
      employment_status: 'inactivo'
    })
  },

  // Reactivar empleado
  async reactivate(id: string) {
    return this.update(id, {
      is_active: true,
      employment_status: 'activo'
    })
  },

  // Cambiar rol de empleado (solo admins)
  async changeRole(id: string, newRole: UserRole) {
    return this.update(id, { role: newRole })
  },

  // Buscar empleados por nombre
  async searchByName(searchTerm: string) {
    const { data, error } = await supabase
      .from('employee_view')
      .select('*')
      .ilike('full_name', `%${searchTerm}%`)
      .eq('is_active', true)

    if (error) throw error
    return data as EmployeeView[]
  },

  // Buscar empleados por número de documento
  async searchByDocument(documentNumber: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('document_number', documentNumber)
      .single()

    if (error) throw error
    return data as Profile
  }
}

// Funciones para manejar departamentos
export const departmentService = {
  // Obtener todos los departamentos
  async getAll() {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data as Department[]
  },

  // Crear nuevo departamento (solo admins)
  async create(department: Omit<Department, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('departments')
      .insert(department)
      .select()
      .single()

    if (error) throw error
    return data as Department
  },

  // Actualizar departamento (solo admins)
  async update(id: string, updates: Partial<Department>) {
    const { data, error } = await supabase
      .from('departments')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Department
  }
}

// Funciones para manejar posiciones de trabajo
export const jobPositionService = {
  // Obtener todas las posiciones
  async getAll() {
    const { data, error } = await supabase
      .from('job_positions')
      .select(`
        *,
        departments (
          name,
          description
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Obtener posiciones por departamento
  async getByDepartment(departmentId: string) {
    const { data, error } = await supabase
      .from('job_positions')
      .select('*')
      .eq('department_id', departmentId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data as JobPosition[]
  },

  // Crear nueva posición (solo admins)
  async create(position: Omit<JobPosition, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('job_positions')
      .insert(position)
      .select()
      .single()

    if (error) throw error
    return data as JobPosition
  }
}

// Funciones para manejar perfiles (usuario actual)
export const profilesService = {
  // Obtener perfil del usuario actual
  async getMyProfile() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) throw error
    return data as Profile
  },

  // Actualizar perfil del usuario actual (campos básicos)
  async updateMyProfile(updates: Partial<Profile>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    // Filtrar solo campos que el usuario puede actualizar
    const allowedFields = {
      first_name: updates.first_name,
      second_name: updates.second_name,
      last_name: updates.last_name,
      second_last_name: updates.second_last_name,
      phone: updates.phone,
      mobile_phone: updates.mobile_phone,
      emergency_contact_name: updates.emergency_contact_name,
      emergency_contact_phone: updates.emergency_contact_phone,
      address: updates.address,
      city: updates.city,
      state: updates.state,
      postal_code: updates.postal_code,
      avatar_url: updates.avatar_url,
      updated_by: user.id
    }

    // Remover campos undefined
    const cleanUpdates = Object.fromEntries(
      Object.entries(allowedFields).filter(([_, value]) => value !== undefined)
    )

    const { data, error } = await supabase
      .from('profiles')
      .update(cleanUpdates)
      .eq('id', user.id)
      .select()
      .single()

    if (error) throw error
    return data as Profile
  }
}

// Funciones de autenticación
export const authService = {
  // Registrarse con email y contraseña
  async signUp(email: string, password: string, firstName?: string, lastName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          full_name: firstName && lastName ? `${firstName} ${lastName}` : undefined
        }
      }
    })

    if (error) throw error
    return data
  },

  // Iniciar sesión
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) throw error
    return data
  },

  // Cerrar sesión
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Obtener usuario actual
  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  },

  // Escuchar cambios de autenticación
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Funciones de utilidad
export const utilityService = {
  // Generar nombre completo
  getFullName(profile: Profile): string {
    const parts = [
      profile.first_name,
      profile.second_name,
      profile.last_name,
      profile.second_last_name
    ].filter(Boolean)

    return parts.join(' ')
  },

  // Calcular edad a partir de fecha de nacimiento
  calculateAge(birthDate: string): number {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  },

  // Formatear número de teléfono
  formatPhoneNumber(phone: string): string {
    // Remover caracteres no numéricos
    const cleaned = phone.replace(/\D/g, '')

    // Formatear para Colombia (ejemplo: +57 ************)
    if (cleaned.length === 10) {
      return `+57 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`
    }

    return phone
  },

  // Validar número de documento
  validateDocument(documentType: string, documentNumber: string): boolean {
    if (!documentNumber) return false

    switch (documentType) {
      case 'cedula':
        // Validación básica para cédula colombiana
        return /^\d{6,10}$/.test(documentNumber)
      case 'pasaporte':
        // Validación básica para pasaporte
        return /^[A-Z0-9]{6,9}$/.test(documentNumber.toUpperCase())
      case 'licencia':
        // Validación básica para licencia
        return /^\d{8,12}$/.test(documentNumber)
      default:
        return false
    }
  },

  // Generar horario de trabajo por defecto
  generateDefaultSchedule(jobPosition: JobPositionType): any {
    const schedules = {
      gerente: {
        monday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        tuesday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        wednesday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        thursday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        friday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        saturday: { start: '08:00', end: '14:00' },
        sunday: { off: true }
      },
      recepcionista: {
        // Turnos rotativos
        shift_type: 'rotating',
        shifts: [
          { name: 'morning', start: '06:00', end: '14:00' },
          { name: 'afternoon', start: '14:00', end: '22:00' },
          { name: 'night', start: '22:00', end: '06:00' }
        ]
      },
      mucama: {
        monday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        tuesday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        wednesday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        thursday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        friday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        saturday: { start: '08:00', end: '14:00' },
        sunday: { off: true }
      },
      guardia: {
        shift_type: 'rotating',
        shifts: [
          { name: 'day', start: '06:00', end: '18:00' },
          { name: 'night', start: '18:00', end: '06:00' }
        ]
      }
    }

    return schedules[jobPosition] || schedules.gerente
  }
}

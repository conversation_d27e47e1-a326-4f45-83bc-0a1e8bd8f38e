import { supabase, type Profile, type Department, type JobPosition, type EmployeeView, type Room, type RoomStatusHistory, type UserRole, type JobPositionType, type RoomStatus, type RoomType } from './supabase'

// Funciones para manejar empleados/perfiles
export const employeeService = {
  // Obtener todos los empleados (solo admins y supervisores)
  async getAll() {
    const { data, error } = await supabase
      .from('employee_view')
      .select('*')
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as EmployeeView[]
  },

  // Obtener empleados activos
  async getActive() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Obtener empleado por ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Profile
  },

  // Obtener empleados por departamento
  async getByDepartment(department: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('department', department)
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Obtener empleados por posición
  async getByJobPosition(jobPosition: JobPositionType) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('job_position', jobPosition)
      .eq('is_active', true)
      .order('last_name', { ascending: true })

    if (error) throw error
    return data as Profile[]
  },

  // Crear nuevo empleado (solo admins)
  async create(employee: Omit<Profile, 'id' | 'created_at' | 'updated_at' | 'employee_id'>) {
    // Generar ID de empleado automáticamente
    const { data: employeeId, error: idError } = await supabase
      .rpc('generate_employee_id')

    if (idError) throw idError

    // Obtener el ID del perfil actual para auditoría
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    const { data, error } = await supabase
      .from('profiles')
      .insert({
        ...employee,
        employee_id: employeeId,
        hire_date: employee.hire_date || new Date().toISOString().split('T')[0],
        created_by: currentProfileId
      })
      .select()
      .single()

    if (error) throw error
    return data as Profile
  },

  // Actualizar empleado
  async update(id: number, updates: Partial<Profile>) {
    // Obtener el ID del perfil actual para auditoría
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_by: currentProfileId
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Profile
  },

  // Desactivar empleado (soft delete)
  async deactivate(id: number) {
    return this.update(id, {
      is_active: false,
      employment_status: 'inactivo'
    })
  },

  // Reactivar empleado
  async reactivate(id: number) {
    return this.update(id, {
      is_active: true,
      employment_status: 'activo'
    })
  },

  // Cambiar rol de empleado (solo admins)
  async changeRole(id: number, newRole: UserRole) {
    return this.update(id, { role: newRole })
  },

  // Buscar empleados por nombre
  async searchByName(searchTerm: string) {
    const { data, error } = await supabase
      .from('employee_view')
      .select('*')
      .ilike('full_name', `%${searchTerm}%`)
      .eq('is_active', true)

    if (error) throw error
    return data as EmployeeView[]
  },

  // Buscar empleados por número de documento
  async searchByDocument(documentNumber: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('document_number', documentNumber)
      .single()

    if (error) throw error
    return data as Profile
  }
}

// Funciones para manejar departamentos
export const departmentService = {
  // Obtener todos los departamentos
  async getAll() {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data as Department[]
  },

  // Crear nuevo departamento (solo admins)
  async create(department: Omit<Department, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('departments')
      .insert(department)
      .select()
      .single()

    if (error) throw error
    return data as Department
  },

  // Actualizar departamento (solo admins)
  async update(id: string, updates: Partial<Department>) {
    const { data, error } = await supabase
      .from('departments')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Department
  }
}

// Funciones para manejar posiciones de trabajo
export const jobPositionService = {
  // Obtener todas las posiciones
  async getAll() {
    const { data, error } = await supabase
      .from('job_positions')
      .select(`
        *,
        departments (
          name,
          description
        )
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Obtener posiciones por departamento
  async getByDepartment(departmentId: string) {
    const { data, error } = await supabase
      .from('job_positions')
      .select('*')
      .eq('department_id', departmentId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data as JobPosition[]
  },

  // Crear nueva posición (solo admins)
  async create(position: Omit<JobPosition, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('job_positions')
      .insert(position)
      .select()
      .single()

    if (error) throw error
    return data as JobPosition
  }
}

// Funciones para manejar perfiles (usuario actual)
export const profilesService = {
  // Obtener perfil del usuario actual (crea uno si no existe)
  async getMyProfile() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    // Primero intentar obtener el perfil existente
    let { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    // Si no existe, usar la función para crearlo automáticamente
    if (error && error.code === 'PGRST116') {
      const { data: newProfile, error: createError } = await supabase
        .rpc('ensure_user_profile')

      if (createError) throw createError
      return newProfile as Profile
    }

    if (error) throw error
    return data as Profile
  },

  // Actualizar perfil del usuario actual (campos básicos)
  async updateMyProfile(updates: Partial<Profile>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    // Obtener el ID del perfil actual
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    // Filtrar solo campos que el usuario puede actualizar
    const allowedFields = {
      first_name: updates.first_name,
      second_name: updates.second_name,
      last_name: updates.last_name,
      second_last_name: updates.second_last_name,
      phone: updates.phone,
      mobile_phone: updates.mobile_phone,
      emergency_contact_name: updates.emergency_contact_name,
      emergency_contact_phone: updates.emergency_contact_phone,
      address: updates.address,
      city: updates.city,
      state: updates.state,
      postal_code: updates.postal_code,
      avatar_url: updates.avatar_url,
      updated_by: currentProfileId
    }

    // Remover campos undefined
    const cleanUpdates = Object.fromEntries(
      Object.entries(allowedFields).filter(([_, value]) => value !== undefined)
    )

    const { data, error } = await supabase
      .from('profiles')
      .update(cleanUpdates)
      .eq('auth_user_id', user.id)
      .select()
      .single()

    if (error) throw error
    return data as Profile
  }
}

// Funciones de autenticación
export const authService = {
  // Registrarse con email y contraseña
  async signUp(email: string, password: string, firstName?: string, lastName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          full_name: firstName && lastName ? `${firstName} ${lastName}` : undefined
        }
      }
    })

    if (error) throw error
    return data
  },

  // Iniciar sesión
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) throw error
    return data
  },

  // Cerrar sesión
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Obtener usuario actual
  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  },

  // Escuchar cambios de autenticación
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Funciones de utilidad
export const utilityService = {
  // Generar nombre completo
  getFullName(profile: Profile): string {
    const parts = [
      profile.first_name,
      profile.second_name,
      profile.last_name,
      profile.second_last_name
    ].filter(Boolean)

    return parts.join(' ')
  },

  // Calcular edad a partir de fecha de nacimiento
  calculateAge(birthDate: string): number {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }

    return age
  },

  // Formatear número de teléfono
  formatPhoneNumber(phone: string): string {
    // Remover caracteres no numéricos
    const cleaned = phone.replace(/\D/g, '')

    // Formatear para Colombia (ejemplo: +57 ************)
    if (cleaned.length === 10) {
      return `+57 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`
    }

    return phone
  },

  // Validar número de documento
  validateDocument(documentType: string, documentNumber: string): boolean {
    if (!documentNumber) return false

    switch (documentType) {
      case 'cedula':
        // Validación básica para cédula colombiana
        return /^\d{6,10}$/.test(documentNumber)
      case 'pasaporte':
        // Validación básica para pasaporte
        return /^[A-Z0-9]{6,9}$/.test(documentNumber.toUpperCase())
      case 'licencia':
        // Validación básica para licencia
        return /^\d{8,12}$/.test(documentNumber)
      default:
        return false
    }
  },

  // Generar horario de trabajo por defecto
  generateDefaultSchedule(jobPosition: JobPositionType): any {
    const schedules = {
      gerente: {
        monday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        tuesday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        wednesday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        thursday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        friday: { start: '08:00', end: '18:00', break: '12:00-13:00' },
        saturday: { start: '08:00', end: '14:00' },
        sunday: { off: true }
      },
      recepcionista: {
        // Turnos rotativos
        shift_type: 'rotating',
        shifts: [
          { name: 'morning', start: '06:00', end: '14:00' },
          { name: 'afternoon', start: '14:00', end: '22:00' },
          { name: 'night', start: '22:00', end: '06:00' }
        ]
      },
      mucama: {
        monday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        tuesday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        wednesday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        thursday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        friday: { start: '08:00', end: '16:00', break: '12:00-13:00' },
        saturday: { start: '08:00', end: '14:00' },
        sunday: { off: true }
      },
      guardia: {
        shift_type: 'rotating',
        shifts: [
          { name: 'day', start: '06:00', end: '18:00' },
          { name: 'night', start: '18:00', end: '06:00' }
        ]
      }
    }

    return schedules[jobPosition] || schedules.gerente
  }
}

// Funciones para manejar habitaciones
export const roomService = {
  // Obtener todas las habitaciones
  async getAll() {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('is_active', true)
      .order('room_number')

    if (error) throw error
    return data as Room[]
  },

  // Obtener habitaciones por piso
  async getByFloor(floorNumber: number) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('floor_number', floorNumber)
      .eq('is_active', true)
      .order('room_number')

    if (error) throw error
    return data as Room[]
  },

  // Obtener habitaciones por estado
  async getByStatus(status: RoomStatus) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('status', status)
      .eq('is_active', true)
      .order('room_number')

    if (error) throw error
    return data as Room[]
  },

  // Obtener habitación por ID
  async getById(id: number) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data as Room
  },

  // Obtener habitación por número
  async getByRoomNumber(roomNumber: string) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('room_number', roomNumber)
      .single()

    if (error) throw error
    return data as Room
  },

  // Crear nueva habitación (solo admins y supervisores)
  async create(room: Omit<Room, 'id' | 'created_at' | 'updated_at'>) {
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    const { data, error } = await supabase
      .from('rooms')
      .insert({
        ...room,
        created_by: currentProfileId
      })
      .select()
      .single()

    if (error) throw error
    return data as Room
  },

  // Actualizar habitación
  async update(id: number, updates: Partial<Room>) {
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    const { data, error } = await supabase
      .from('rooms')
      .update({
        ...updates,
        updated_by: currentProfileId
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Room
  },

  // Cambiar estado de habitación
  async changeStatus(id: number, newStatus: RoomStatus, reason?: string, notes?: string) {
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    // Primero obtener el estado actual
    const currentRoom = await this.getById(id)

    // Actualizar el estado
    const { data, error } = await supabase
      .from('rooms')
      .update({
        status: newStatus,
        updated_by: currentProfileId
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    // Registrar el cambio manualmente si se proporciona razón o notas adicionales
    if (reason || notes) {
      await supabase
        .from('room_status_history')
        .insert({
          room_id: id,
          previous_status: currentRoom.status,
          new_status: newStatus,
          changed_by: currentProfileId!,
          change_reason: reason,
          notes: notes
        })
    }

    return data as Room
  },

  // Eliminar habitación (soft delete)
  async delete(id: number) {
    const { data: currentProfileId } = await supabase
      .rpc('get_current_profile_id')

    const { data, error } = await supabase
      .from('rooms')
      .update({
        is_active: false,
        updated_by: currentProfileId
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as Room
  },

  // Obtener estadísticas de habitaciones
  async getStatistics() {
    const { data, error } = await supabase
      .from('rooms')
      .select('status, room_type, floor_number')
      .eq('is_active', true)

    if (error) throw error

    const stats = {
      total: data.length,
      byStatus: {} as Record<RoomStatus, number>,
      byType: {} as Record<RoomType, number>,
      byFloor: {} as Record<number, number>
    }

    // Inicializar contadores
    const statuses: RoomStatus[] = ['libre', 'ocupado', 'reservado', 'mantenimiento']
    const types: RoomType[] = ['individual', 'doble', 'suite', 'familiar', 'presidencial']

    statuses.forEach(status => stats.byStatus[status] = 0)
    types.forEach(type => stats.byType[type] = 0)

    // Contar
    data.forEach(room => {
      stats.byStatus[room.status as RoomStatus]++
      stats.byType[room.room_type as RoomType]++
      stats.byFloor[room.floor_number] = (stats.byFloor[room.floor_number] || 0) + 1
    })

    return stats
  }
}

// Funciones para manejar historial de estados de habitaciones
export const roomHistoryService = {
  // Obtener historial de una habitación
  async getRoomHistory(roomId: number, limit: number = 50) {
    const { data, error } = await supabase
      .from('room_status_history')
      .select(`
        *,
        profiles!changed_by (
          first_name,
          last_name,
          employee_id
        )
      `)
      .eq('room_id', roomId)
      .order('changed_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Obtener historial reciente de todas las habitaciones
  async getRecentHistory(limit: number = 100) {
    const { data, error } = await supabase
      .from('room_status_history')
      .select(`
        *,
        rooms (
          room_number,
          floor_number
        ),
        profiles!changed_by (
          first_name,
          last_name,
          employee_id
        )
      `)
      .order('changed_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Obtener cambios por empleado
  async getChangesByEmployee(employeeId: number, limit: number = 50) {
    const { data, error } = await supabase
      .from('room_status_history')
      .select(`
        *,
        rooms (
          room_number,
          floor_number
        )
      `)
      .eq('changed_by', employeeId)
      .order('changed_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data
  },

  // Obtener cambios por fecha
  async getChangesByDate(startDate: string, endDate: string) {
    const { data, error } = await supabase
      .from('room_status_history')
      .select(`
        *,
        rooms (
          room_number,
          floor_number
        ),
        profiles!changed_by (
          first_name,
          last_name,
          employee_id
        )
      `)
      .gte('changed_at', startDate)
      .lte('changed_at', endDate)
      .order('changed_at', { ascending: false })

    if (error) throw error
    return data
  }
}

// Funciones para gestión de roles y usuarios
export const userManagementService = {
  // Cambiar rol de usuario
  async changeUserRole(email: string, newRole: UserRole) {
    const { data, error } = await supabase
      .rpc('change_user_role', {
        user_email: email,
        new_role: newRole
      })

    if (error) throw error
    return data as Profile
  },

  // Obtener usuarios que pueden ser promovidos
  async getUsersForPromotion() {
    const { data, error } = await supabase
      .rpc('get_users_for_promotion')

    if (error) throw error
    return data
  },

  // Asegurar que el usuario actual tiene perfil
  async ensureCurrentUserProfile() {
    const { data, error } = await supabase
      .rpc('ensure_user_profile')

    if (error) throw error
    return data as Profile
  }
}

import { supabase, type Room, type Booking, type Profile } from './supabase'

// Funciones para manejar habitaciones
export const roomsService = {
  // Obtener todas las habitaciones
  async getAll() {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .order('room_number')
    
    if (error) throw error
    return data as Room[]
  },

  // Obtener habitaciones disponibles
  async getAvailable() {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('is_available', true)
      .order('room_number')
    
    if (error) throw error
    return data as Room[]
  },

  // Obtener habitación por ID
  async getById(id: string) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return data as Room
  },

  // Crear nueva habitación (solo admins)
  async create(room: Omit<Room, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('rooms')
      .insert(room)
      .select()
      .single()
    
    if (error) throw error
    return data as Room
  },

  // Actualizar habitación (solo admins)
  async update(id: string, updates: Partial<Room>) {
    const { data, error } = await supabase
      .from('rooms')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as Room
  },

  // Eliminar habitación (solo admins)
  async delete(id: string) {
    const { error } = await supabase
      .from('rooms')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}

// Funciones para manejar reservas
export const bookingsService = {
  // Obtener todas las reservas del usuario actual
  async getMyBookings() {
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        rooms (
          room_number,
          room_type,
          price_per_night
        )
      `)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Obtener todas las reservas (solo admins)
  async getAll() {
    const { data, error } = await supabase
      .from('bookings')
      .select(`
        *,
        rooms (
          room_number,
          room_type,
          price_per_night
        ),
        profiles (
          full_name,
          email
        )
      `)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Crear nueva reserva
  async create(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('bookings')
      .insert(booking)
      .select()
      .single()
    
    if (error) throw error
    return data as Booking
  },

  // Actualizar reserva
  async update(id: string, updates: Partial<Booking>) {
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data as Booking
  },

  // Cancelar reserva
  async cancel(id: string) {
    return this.update(id, { status: 'cancelled' })
  },

  // Confirmar reserva (solo admins)
  async confirm(id: string) {
    return this.update(id, { status: 'confirmed' })
  }
}

// Funciones para manejar perfiles
export const profilesService = {
  // Obtener perfil del usuario actual
  async getMyProfile() {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()
    
    if (error) throw error
    return data as Profile
  },

  // Actualizar perfil del usuario actual
  async updateMyProfile(updates: Partial<Profile>) {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('No user logged in')

    const { data, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id)
      .select()
      .single()
    
    if (error) throw error
    return data as Profile
  }
}

// Funciones de autenticación
export const authService = {
  // Registrarse con email y contraseña
  async signUp(email: string, password: string, fullName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName
        }
      }
    })
    
    if (error) throw error
    return data
  },

  // Iniciar sesión
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) throw error
    return data
  },

  // Cerrar sesión
  async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  // Obtener usuario actual
  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  },

  // Escuchar cambios de autenticación
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// Función para verificar disponibilidad de habitación
export async function checkRoomAvailability(
  roomId: string, 
  checkInDate: string, 
  checkOutDate: string
): Promise<boolean> {
  const { data, error } = await supabase
    .from('bookings')
    .select('id')
    .eq('room_id', roomId)
    .in('status', ['pending', 'confirmed'])
    .or(`check_in_date.lte.${checkOutDate},check_out_date.gte.${checkInDate}`)
  
  if (error) throw error
  return data.length === 0
}

// Función para calcular precio total de reserva
export function calculateTotalPrice(
  pricePerNight: number, 
  checkInDate: string, 
  checkOutDate: string
): number {
  const checkIn = new Date(checkInDate)
  const checkOut = new Date(checkOutDate)
  const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24))
  return pricePerNight * nights
}

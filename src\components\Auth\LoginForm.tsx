'use client'

import { useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Auth } from '@supabase/auth-ui-react'
import { ThemeSupa } from '@supabase/auth-ui-shared'

export default function LoginForm() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleGoogleLogin = async () => {
    setLoading(true)
    setError(null) // Limpiar errores previos
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      })
      if (error) throw error
    } catch (error: any) {
      console.error('Error during Google login:', error)
      setError(error.message || 'Error al iniciar sesión con Google')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto">
      {/* Tarjeta principal con efecto de cristal */}
      <div className="card-luxury p-8 backdrop-blur-luxury border border-white/20">
        {/* Header con logo */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-luxury animate-float">
              <span className="text-white font-luxury font-bold text-2xl">H</span>
            </div>
          </div>

          <h2 className="text-3xl font-luxury font-bold text-dark-900 mb-2">
            Bienvenido
          </h2>
          <p className="text-dark-600">
            Hotel Luxury Management System
          </p>
        </div>

        {/* Botón de Google mejorado */}
        <button
          onClick={handleGoogleLogin}
          disabled={loading}
          className="w-full mb-6 bg-white hover:bg-gray-50 disabled:bg-gray-100 text-dark-700 font-medium py-4 px-6 rounded-xl border-2 border-dark-200 hover:border-primary-300 flex items-center justify-center gap-3 transition-all duration-300 transform hover:scale-105 shadow-card hover:shadow-card-hover disabled:transform-none"
        >
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="currentColor"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="currentColor"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="currentColor"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary-600 border-t-transparent"></div>
              <span>Iniciando sesión...</span>
            </>
          ) : (
            <>
              <span>🚀</span>
              <span>Continuar con Google</span>
            </>
          )}
        </button>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg">
            <div className="flex items-center">
              <span className="text-red-500 mr-2">⚠️</span>
              <span className="text-red-700 font-medium">{error}</span>
            </div>
          </div>
        )}

        <div className="relative my-8">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-dark-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-white text-dark-500 font-medium">O continúa con email</span>
          </div>
        </div>

      {/* Componente Auth UI de Supabase */}
      <Auth
        supabaseClient={supabase}
        appearance={{
          theme: ThemeSupa,
          variables: {
            default: {
              colors: {
                brand: '#dc2626',
                brandAccent: '#b91c1c',
              },
            },
          },
          className: {
            container: 'space-y-4',
            button: 'btn-luxury w-full',
            input: 'input-luxury',
          }
        }}
        providers={[]}
        redirectTo={`${typeof window !== 'undefined' ? window.location.origin : ''}/dashboard`}
        onlyThirdPartyProviders={false}
        localization={{
          variables: {
            sign_in: {
              email_label: 'Correo electrónico',
              password_label: 'Contraseña',
              button_label: 'Iniciar sesión',
              loading_button_label: 'Iniciando sesión...',
              link_text: '¿Ya tienes una cuenta? Inicia sesión',
            },
            sign_up: {
              email_label: 'Correo electrónico',
              password_label: 'Contraseña',
              button_label: 'Registrarse',
              loading_button_label: 'Registrándose...',
              link_text: '¿No tienes una cuenta? Regístrate',
            },
          },
        }}
      />

        {/* Información adicional */}
        <div className="mt-8 space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <p className="text-sm text-blue-800 font-medium mb-2">
              ✨ Acceso Seguro
            </p>
            <p className="text-xs text-blue-700">
              Utiliza tu cuenta de Google o email para acceder de forma segura al sistema.
            </p>
          </div>

          <div className="p-4 bg-amber-50 border border-amber-200 rounded-xl">
            <p className="text-sm text-amber-800 font-medium mb-2">
              🆕 Primera vez?
            </p>
            <p className="text-xs text-amber-700">
              Se creará automáticamente una cuenta. Contacta al administrador para obtener permisos especiales.
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 text-center">
        <p className="text-sm text-dark-400">
          © 2025 Hotel Luxury Management System
        </p>
        <p className="text-xs text-dark-500 mt-1">
          Sistema de gestión hotelera de 5 estrellas
        </p>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { roomChangeLogsService } from '@/lib/supabase-utils'

interface RoomChangeLog {
  id: number
  room_id: number
  previous_status: string | null
  new_status: string
  change_type: string
  executed_at: string
  user_name: string | null
  user_role: string | null
  reason: string | null
  notes: string | null
  additional_data: any
  room?: { room_number: string; floor_number: number }
  occupancy?: { guest_name: string | null; guest_count: number | null }
}

interface RoomChangeLogsProps {
  roomId?: number
  limit?: number
}

export default function RoomChangeLogs({ roomId, limit = 50 }: RoomChangeLogsProps) {
  const [logs, setLogs] = useState<RoomChangeLog[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadLogs()
  }, [roomId, limit])

  const loadLogs = async () => {
    try {
      setLoading(true)
      const data = roomId 
        ? await roomChangeLogsService.getRoomLogs(roomId, limit)
        : await roomChangeLogsService.getRecentLogs(limit)
      setLogs(data)
    } catch (error) {
      console.error('Error loading logs:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'libre': return 'bg-green-100 text-green-800'
      case 'ocupado': return 'bg-red-100 text-red-800'
      case 'reservado': return 'bg-blue-100 text-blue-800'
      case 'mantenimiento': return 'bg-orange-100 text-orange-800'
      default: return 'bg-dark-100 text-dark-800'
    }
  }

  const getChangeTypeIcon = (type: string) => {
    switch (type) {
      case 'manual': return '👤'
      case 'automatic': return '🤖'
      case 'scheduled': return '⏰'
      default: return '📝'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('es-CO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getActionDescription = (log: RoomChangeLog) => {
    const additionalData = log.additional_data || {}
    const action = additionalData.action

    switch (action) {
      case 'reserve':
        return `Reserva creada para ${additionalData.guest_name || 'huésped'}`
      case 'checkin':
        return `Check-in realizado para ${additionalData.guest_name || 'huésped'}`
      case 'checkout':
        return 'Check-out completado'
      case 'maintenance':
        return 'Enviado a mantenimiento'
      case 'clean':
        return 'Marcado como limpio'
      default:
        return log.reason || 'Cambio de estado'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-dark-900">
          {roomId ? `Historial de Cambios - Habitación` : 'Actividad Reciente'}
        </h3>
        <p className="text-sm text-black mt-1">
          {roomId
            ? 'Todos los cambios realizados en esta habitación'
            : 'Últimos cambios en el sistema'
          }
        </p>
      </div>

      <div className="p-6">
        {logs.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-dark-400 text-4xl mb-4">📋</div>
            <h4 className="text-lg font-medium text-dark-900 mb-2">
              No hay registros
            </h4>
            <p className="text-black">
              No se han registrado cambios aún.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {logs.map((log) => (
              <div
                key={log.id}
                className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                {/* Icono del tipo de cambio */}
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="text-lg">{getChangeTypeIcon(log.change_type)}</span>
                  </div>
                </div>

                {/* Contenido principal */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {log.room && (
                        <span className="text-sm font-medium text-dark-900">
                          Habitación {log.room.room_number}
                        </span>
                      )}
                      
                      {/* Estados */}
                      <div className="flex items-center space-x-2">
                        {log.previous_status && (
                          <>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(log.previous_status)}`}>
                              {log.previous_status}
                            </span>
                            <span className="text-dark-400">→</span>
                          </>
                        )}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(log.new_status)}`}>
                          {log.new_status}
                        </span>
                      </div>
                    </div>

                    <span className="text-xs text-dark-600">
                      {formatDate(log.executed_at)}
                    </span>
                  </div>

                  {/* Descripción */}
                  <p className="text-sm text-dark-700 mt-1">
                    {getActionDescription(log)}
                  </p>

                  {/* Usuario */}
                  <div className="flex items-center space-x-4 mt-2 text-xs text-dark-600">
                    <span>
                      Por: {log.user_name || 'Sistema'}
                      {log.user_role && (
                        <span className="ml-1 px-1.5 py-0.5 bg-dark-100 rounded text-xs">
                          {log.user_role}
                        </span>
                      )}
                    </span>
                    
                    <span className="px-1.5 py-0.5 bg-dark-100 rounded">
                      {log.change_type === 'manual' ? 'Manual' : 
                       log.change_type === 'automatic' ? 'Automático' : 
                       'Programado'}
                    </span>
                  </div>

                  {/* Información adicional */}
                  {log.additional_data && (
                    <div className="mt-2 text-xs text-dark-600">
                      {log.additional_data.guest_name && (
                        <div>Huésped: {log.additional_data.guest_name}</div>
                      )}
                      {log.additional_data.guest_count && log.additional_data.guest_count > 1 && (
                        <div>Personas: {log.additional_data.guest_count}</div>
                      )}
                      {log.additional_data.check_in_date && (
                        <div>
                          Check-in: {log.additional_data.check_in_date} {log.additional_data.check_in_time}
                        </div>
                      )}
                      {log.additional_data.check_out_date && (
                        <div>
                          Check-out: {log.additional_data.check_out_date} {log.additional_data.check_out_time}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Notas */}
                  {log.notes && (
                    <div className="mt-2 p-2 bg-dark-50 rounded text-xs text-dark-700">
                      <strong>Notas:</strong> {log.notes}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Botón para cargar más */}
        {logs.length >= limit && (
          <div className="mt-6 text-center">
            <button
              onClick={() => loadLogs()}
              className="px-4 py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Cargar más registros
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { roomService } from '@/lib/supabase-utils'
import type { Room } from '@/lib/supabase'

interface RoomManagementProps {
  onRoomsChange: () => void
}

interface RoomFormData {
  room_number: string
  floor_number: number
  room_type: 'individual' | 'doble' | 'suite' | 'familiar' | 'presidencial'
  capacity: number
  price_per_night: number
  description: string
  amenities: string[]
}

const ROOM_TYPES = [
  { value: 'individual', label: 'Individual', icon: '🛏️' },
  { value: 'doble', label: 'Doble', icon: '🛏️🛏️' },
  { value: 'suite', label: 'Suite', icon: '🏨' },
  { value: 'familiar', label: 'Familiar', icon: '👨‍👩‍👧‍👦' },
  { value: 'presidencial', label: 'Presidencial', icon: '👑' }
]

const AVAILABLE_AMENITIES = [
  { value: 'wifi', label: 'WiFi', icon: '📶' },
  { value: 'tv', label: 'TV', icon: '📺' },
  { value: 'aire_acondicionado', label: 'Aire Acondicionado', icon: '❄️' },
  { value: 'minibar', label: 'Minibar', icon: '🍷' },
  { value: 'balcon', label: 'Balcón', icon: '🌅' },
  { value: 'jacuzzi', label: 'Jacuzzi', icon: '🛁' },
  { value: 'vista_mar', label: 'Vista al Mar', icon: '🌊' },
  { value: 'vista_ciudad', label: 'Vista a la Ciudad', icon: '🏙️' },
  { value: 'caja_fuerte', label: 'Caja Fuerte', icon: '🔒' },
  { value: 'servicio_habitacion', label: 'Servicio a la Habitación', icon: '🛎️' }
]

export default function RoomManagement({ onRoomsChange }: RoomManagementProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [editingRoom, setEditingRoom] = useState<Room | null>(null)
  const [formData, setFormData] = useState<RoomFormData>({
    room_number: '',
    floor_number: 1,
    room_type: 'individual',
    capacity: 1,
    price_per_night: 80000,
    description: '',
    amenities: ['wifi', 'tv', 'aire_acondicionado']
  })

  const resetForm = () => {
    setFormData({
      room_number: '',
      floor_number: 1,
      room_type: 'individual',
      capacity: 1,
      price_per_night: 80000,
      description: '',
      amenities: ['wifi', 'tv', 'aire_acondicionado']
    })
    setEditingRoom(null)
  }

  const openAddModal = () => {
    resetForm()
    setIsOpen(true)
  }

  const openEditModal = (room: Room) => {
    setFormData({
      room_number: room.room_number,
      floor_number: room.floor_number,
      room_type: room.room_type,
      capacity: room.capacity,
      price_per_night: room.price_per_night,
      description: room.description || '',
      amenities: room.amenities || []
    })
    setEditingRoom(room)
    setIsOpen(true)
  }

  const closeModal = () => {
    setIsOpen(false)
    resetForm()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      if (editingRoom) {
        // Actualizar habitación existente
        await roomService.update(editingRoom.id, formData)
      } else {
        // Crear nueva habitación
        await roomService.create({
          ...formData,
          status: 'libre',
          is_active: true
        })
      }
      
      onRoomsChange()
      closeModal()
    } catch (error) {
      console.error('Error al guardar habitación:', error)
      alert('Error al guardar la habitación. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }))
  }

  const handleDeleteRoom = async (room: Room) => {
    if (!confirm(`¿Estás seguro de que quieres eliminar la habitación ${room.room_number}?`)) {
      return
    }

    try {
      await roomService.delete(room.id)
      onRoomsChange()
    } catch (error) {
      console.error('Error al eliminar habitación:', error)
      alert('Error al eliminar la habitación. Por favor, intenta de nuevo.')
    }
  }

  if (!isOpen) {
    return (
      <div className="flex gap-3">
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center space-x-2"
        >
          <span>➕</span>
          <span>Agregar Habitación</span>
        </button>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-luxury max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-luxury font-bold text-dark-900">
              {editingRoom ? '✏️ Editar Habitación' : '➕ Agregar Nueva Habitación'}
            </h2>
            <button
              onClick={closeModal}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ✕
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Información básica */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Número de Habitación
              </label>
              <input
                type="text"
                value={formData.room_number}
                onChange={(e) => setFormData(prev => ({ ...prev, room_number: e.target.value }))}
                className="input-luxury"
                placeholder="Ej: 101, 201A"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Piso
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={formData.floor_number}
                onChange={(e) => setFormData(prev => ({ ...prev, floor_number: parseInt(e.target.value) }))}
                className="input-luxury"
                required
              />
            </div>
          </div>

          {/* Tipo y capacidad */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipo de Habitación
              </label>
              <select
                value={formData.room_type}
                onChange={(e) => setFormData(prev => ({ ...prev, room_type: e.target.value as any }))}
                className="input-luxury"
                required
              >
                {ROOM_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Capacidad (personas)
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.capacity}
                onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) }))}
                className="input-luxury"
                required
              />
            </div>
          </div>

          {/* Precio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Precio por Noche (COP)
            </label>
            <input
              type="number"
              min="0"
              step="1000"
              value={formData.price_per_night}
              onChange={(e) => setFormData(prev => ({ ...prev, price_per_night: parseFloat(e.target.value) }))}
              className="input-luxury"
              placeholder="80000"
              required
            />
          </div>

          {/* Descripción */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descripción
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="input-luxury"
              rows={3}
              placeholder="Descripción de la habitación..."
            />
          </div>

          {/* Amenidades */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Amenidades
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {AVAILABLE_AMENITIES.map(amenity => (
                <label
                  key={amenity.value}
                  className={`
                    flex items-center space-x-2 p-3 rounded-lg border-2 cursor-pointer transition-all
                    ${formData.amenities.includes(amenity.value)
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <input
                    type="checkbox"
                    checked={formData.amenities.includes(amenity.value)}
                    onChange={() => handleAmenityToggle(amenity.value)}
                    className="sr-only"
                  />
                  <span className="text-lg">{amenity.icon}</span>
                  <span className="text-sm font-medium">{amenity.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Botones */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={closeModal}
              className="btn-secondary"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={isLoading}
            >
              {isLoading ? 'Guardando...' : editingRoom ? 'Actualizar' : 'Crear Habitación'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { roomService } from '@/lib/supabase-utils'
import type { Room } from '@/lib/supabase'
import ConfirmationModal from './ConfirmationModal'

interface RoomManagementProps {
  onRoomsChange: () => void
  editingRoom?: Room | null
  onClose?: () => void
}

interface RoomFormData {
  room_number: string
  floor_number: number
  room_type: 'individual' | 'doble' | 'suite' | 'familiar' | 'presidencial'
  capacity: number
  price_per_night: number
  description: string
  amenities: string[]
}

const ROOM_TYPES = [
  { value: 'individual', label: 'Individual', icon: '🛏️' },
  { value: 'doble', label: 'Doble', icon: '🛏️🛏️' },
  { value: 'suite', label: 'Suite', icon: '🏨' },
  { value: 'familiar', label: 'Familiar', icon: '👨‍👩‍👧‍👦' },
  { value: 'presidencial', label: 'Presidencial', icon: '👑' }
]

const AVAILABLE_AMENITIES = [
  { value: 'wifi', label: 'WiFi', icon: '📶' },
  { value: 'tv', label: 'TV', icon: '📺' },
  { value: 'aire_acondicionado', label: 'Aire Acondicionado', icon: '❄️' },
  { value: 'minibar', label: 'Minibar', icon: '🍷' },
  { value: 'balcon', label: 'Balcón', icon: '🌅' },
  { value: 'jacuzzi', label: 'Jacuzzi', icon: '🛁' },
  { value: 'vista_mar', label: 'Vista al Mar', icon: '🌊' },
  { value: 'vista_ciudad', label: 'Vista a la Ciudad', icon: '🏙️' },
  { value: 'caja_fuerte', label: 'Caja Fuerte', icon: '🔒' },
  { value: 'servicio_habitacion', label: 'Servicio a la Habitación', icon: '🛎️' }
]

export default function RoomManagement({ onRoomsChange, editingRoom: propEditingRoom, onClose }: RoomManagementProps) {
  const [isOpen, setIsOpen] = useState(!!propEditingRoom)
  const [isLoading, setIsLoading] = useState(false)
  const [editingRoom, setEditingRoom] = useState<Room | null>(propEditingRoom || null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [roomToDelete, setRoomToDelete] = useState<Room | null>(null)
  const [formData, setFormData] = useState<RoomFormData>({
    room_number: '',
    floor_number: 1,
    room_type: 'individual',
    capacity: 1,
    price_per_night: 80000,
    description: '',
    amenities: ['wifi', 'tv', 'aire_acondicionado']
  })

  const resetForm = () => {
    setFormData({
      room_number: '',
      floor_number: 1,
      room_type: 'individual',
      capacity: 1,
      price_per_night: 80000,
      description: '',
      amenities: ['wifi', 'tv', 'aire_acondicionado']
    })
    setEditingRoom(null)
  }

  const openAddModal = () => {
    resetForm()
    setIsOpen(true)
  }

  const openEditModal = (room: Room) => {
    setFormData({
      room_number: room.room_number,
      floor_number: room.floor_number,
      room_type: room.room_type,
      capacity: room.capacity,
      price_per_night: room.price_per_night,
      description: room.description || '',
      amenities: room.amenities || []
    })
    setEditingRoom(room)
    setIsOpen(true)
  }

  const closeModal = () => {
    setIsOpen(false)
    resetForm()
    onClose?.()
  }

  // Efecto para manejar cuando se pasa una habitación para editar
  useEffect(() => {
    if (propEditingRoom) {
      setFormData({
        room_number: propEditingRoom.room_number,
        floor_number: propEditingRoom.floor_number,
        room_type: propEditingRoom.room_type,
        capacity: propEditingRoom.capacity,
        price_per_night: propEditingRoom.price_per_night,
        description: propEditingRoom.description || '',
        amenities: propEditingRoom.amenities || []
      })
      setEditingRoom(propEditingRoom)
      setIsOpen(true)
    }
  }, [propEditingRoom])

  const validateForm = () => {
    if (!formData.room_number.trim()) {
      alert('El número de habitación es requerido')
      return false
    }

    if (formData.floor_number < 1) {
      alert('El piso debe ser mayor a 0')
      return false
    }

    if (formData.capacity < 1) {
      alert('La capacidad debe ser mayor a 0')
      return false
    }

    if (formData.price_per_night <= 0) {
      alert('El precio debe ser mayor a 0')
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      if (editingRoom) {
        // Actualizar habitación existente
        await roomService.update(editingRoom.id, formData)
      } else {
        // Crear nueva habitación
        await roomService.create({
          ...formData,
          status: 'libre',
          is_active: true
        })
      }

      onRoomsChange()
      closeModal()
    } catch (error) {
      console.error('Error al guardar habitación:', error)
      alert('Error al guardar la habitación. Por favor, intenta de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }))
  }

  const handleDeleteRoom = (room: Room) => {
    setRoomToDelete(room)
    setShowDeleteConfirm(true)
  }

  const confirmDeleteRoom = async () => {
    if (!roomToDelete) return

    try {
      await roomService.delete(roomToDelete.id)
      onRoomsChange()
      setShowDeleteConfirm(false)
      setRoomToDelete(null)
      closeModal()
    } catch (error) {
      console.error('Error al eliminar habitación:', error)
      alert('Error al eliminar la habitación. Por favor, intenta de nuevo.')
    }
  }

  if (!isOpen) {
    return (
      <div className="flex gap-3">
        <button
          onClick={openAddModal}
          className="btn-primary flex items-center space-x-2"
        >
          <span>➕</span>
          <span>Agregar Habitación</span>
        </button>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-luxury max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-luxury font-bold text-dark-900">
              {editingRoom ? '✏️ Editar Habitación' : '➕ Agregar Nueva Habitación'}
            </h2>
            <button
              onClick={closeModal}
              className="text-dark-600 hover:text-dark-800 text-2xl"
            >
              ✕
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Información básica */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-800 mb-2">
                Número de Habitación
              </label>
              <input
                type="text"
                value={formData.room_number}
                onChange={(e) => setFormData(prev => ({ ...prev, room_number: e.target.value }))}
                className="input-luxury"
                placeholder="Ej: 101, 201A"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-800 mb-2">
                Piso
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={formData.floor_number}
                onChange={(e) => setFormData(prev => ({ ...prev, floor_number: parseInt(e.target.value) }))}
                className="input-luxury"
                required
              />
            </div>
          </div>

          {/* Tipo y capacidad */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-800 mb-2">
                Tipo de Habitación
              </label>
              <select
                value={formData.room_type}
                onChange={(e) => setFormData(prev => ({ ...prev, room_type: e.target.value as any }))}
                className="input-luxury"
                required
              >
                {ROOM_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-800 mb-2">
                Capacidad (personas)
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={formData.capacity}
                onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) }))}
                className="input-luxury"
                required
              />
            </div>
          </div>

          {/* Precio */}
          <div>
            <label className="block text-sm font-medium text-dark-800 mb-2">
              Precio por Noche (CLP)
            </label>
            <input
              type="number"
              min="0"
              step="1000"
              value={formData.price_per_night}
              onChange={(e) => setFormData(prev => ({ ...prev, price_per_night: parseFloat(e.target.value) }))}
              className="input-luxury"
              placeholder="80000"
              required
            />
          </div>

          {/* Descripción */}
          <div>
            <label className="block text-sm font-medium text-dark-800 mb-2">
              Descripción
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="input-luxury"
              rows={3}
              placeholder="Descripción de la habitación..."
            />
          </div>

          {/* Amenidades */}
          <div>
            <label className="block text-sm font-medium text-dark-800 mb-3">
              Amenidades
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {AVAILABLE_AMENITIES.map(amenity => (
                <label
                  key={amenity.value}
                  className={`
                    flex items-center space-x-2 p-3 rounded-lg border-2 cursor-pointer transition-all
                    ${formData.amenities.includes(amenity.value)
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                    }
                  `}
                >
                  <input
                    type="checkbox"
                    checked={formData.amenities.includes(amenity.value)}
                    onChange={() => handleAmenityToggle(amenity.value)}
                    className="sr-only"
                  />
                  <span className="text-lg">{amenity.icon}</span>
                  <span className="text-sm font-medium">{amenity.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Botones */}
          <div className="flex justify-between pt-4 border-t border-gray-200">
            {/* Botón de eliminar (solo en modo edición) */}
            {editingRoom && (
              <button
                type="button"
                onClick={() => handleDeleteRoom(editingRoom)}
                className="px-4 py-2 text-sm font-medium text-red-700 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
                disabled={isLoading}
              >
                🗑️ Eliminar
              </button>
            )}

            <div className="flex space-x-3 ml-auto">
              <button
                type="button"
                onClick={closeModal}
                className="btn-secondary"
                disabled={isLoading}
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="btn-primary"
                disabled={isLoading}
              >
                {isLoading ? 'Guardando...' : editingRoom ? 'Actualizar' : 'Crear Habitación'}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Modal de confirmación para eliminar */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        title="Eliminar Habitación"
        message={`¿Estás seguro de que quieres eliminar la habitación ${roomToDelete?.room_number}? Esta acción no se puede deshacer.`}
        confirmText="Eliminar"
        cancelText="Cancelar"
        type="danger"
        icon="🗑️"
        onConfirm={confirmDeleteRoom}
        onCancel={() => {
          setShowDeleteConfirm(false)
          setRoomToDelete(null)
        }}
      />
    </div>
  )
}
